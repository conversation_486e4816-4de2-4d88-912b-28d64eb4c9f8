{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2021, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "eslint-plugin-import", "eslint-plugin-node", "eslint-plugin-promise"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:import/typescript", "plugin:node/recommended", "plugin:promise/recommended"], "rules": {"no-console": "off", "no-process-exit": "off", "import/no-unresolved": "off", "node/no-missing-import": "off", "@typescript-eslint/no-redeclare": "off", "@typescript-eslint/no-this-alias": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-empty-interface": "off", "node/no-unsupported-features/es-syntax": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-unused-vars": "off", "node/no-unsupported-features/node-builtins": "off", "node/no-unsupported-features/es-builtins": "off", "comma-dangle": "off"}}