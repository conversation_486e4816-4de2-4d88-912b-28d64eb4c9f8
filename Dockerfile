FROM public.ecr.aws/a4u3m1u3/node-20:latest

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy package.json and pnpm-lock.yaml (if using pnpm)
COPY package*.json ./

# Install pnpm globally
RUN npm install -g pnpm

# Install dependencies
RUN pnpm install

# Copy the rest of the application code to the working directory
COPY . .

# Build the project
RUN pnpm build

# Use a non-root user if needed
USER node

# Specify the command to run the application
CMD [ "pnpm", "run", "start" ]
