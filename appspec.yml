version: 0.0
os: linux
files:
  - source: /
    destination: /home/<USER>/provider-portal-chat-module-api
    runas: ubuntu
    overwrite: true
file_exists_behavior: OVERWRITE
permissions:
  - object: /home/<USER>/provider-portal-chat-module-api
    owner: ubuntu
    group: ubuntu

#Used link : https://github.com/aktjerry/bitbucket-to-ec2/blob/master/README.md
hooks:
  AfterInstall:
    - location: update-api-chat-module.sh
      timeout: 300
      runas: ubuntu
