version: 0.2
phases:
  build:
      commands:
        - apt-get update
        - apt-get install -y dos2unix
        - aws s3 cp s3://staging-env-variables-store/update-api-chat-module.sh .
        - dos2unix update-api-chat-module.sh  # Convert line endings to Unix format
        - chmod +x update-api-chat-module.sh
        - docker build --no-cache -t provider-portal-chat-module-api .
        - aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 055726550846.dkr.ecr.us-east-1.amazonaws.com
        - docker tag provider-portal-chat-module-api:latest 055726550846.dkr.ecr.us-east-1.amazonaws.com/provider-portal-chat-module-api:latest
        - docker push 055726550846.dkr.ecr.us-east-1.amazonaws.com/provider-portal-chat-module-api:latest

artifacts:
  files:
  - '**/*'
  - 'update.sh'
  base-directory: .
