{"name": "provider-portal-chat-module-api", "version": "1.0.0", "description": "", "keywords": [], "license": "ISC", "author": "", "main": "index.js", "scripts": {"build": "tsc", "db:generate": "drizzle-kit generate", "db:migrate": "ts-node src/db/migrate.ts", "db:studio": "drizzle-kit studio", "dev": "nodemon src/index.ts  --trace-warnings", "lint": "eslint .", "prettier": "prettier --write .", "start": "node build/index.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.650.0", "@mailchimp/mailchimp_transactional": "^1.0.59", "@socket.io/admin-ui": "^0.5.1", "@socket.io/redis-adapter": "^8.3.0", "@types/mailchimp__mailchimp_transactional": "^1.0.10", "@types/qs": "^6.9.15", "aws-sdk": "^2.1659.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "drizzle-orm": "^0.31.2", "drizzle-zod": "^0.5.1", "express": "^4.19.2", "express-rate-limit": "^7.3.1", "express-validator": "^7.1.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "pg": "^8.12.0", "postgres": "^3.4.4", "qs": "^6.12.3", "request-ip": "^3.3.0", "socket.io": "^4.7.5", "ts-node": "^10.9.2", "twilio": "^5.2.2", "uuid": "^10.0.0", "winston": "^3.13.0", "ws": "^8.17.1", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/multer-s3": "^3.0.0", "@types/node": "^20.14.8", "@types/request-ip": "^0.0.41", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.10", "drizzle-kit": "^0.22.7", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-promise": "^6.1.1", "nodemon": "^3.1.4", "prettier": "^3.3.2", "typescript": "^5.5.2"}}