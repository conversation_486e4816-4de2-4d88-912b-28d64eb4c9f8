import express from 'express';
import cors from 'cors';
import requestIp from 'request-ip';

import logger from './utils/logger/morgan';
import { rootMiddleware } from './middelware/root.middleware';
import notFoundHandler from './middelware/notFound.middleware';
import errorHandler from './middelware/error.middleware';
import limiter from './utils/limiter';
import router from './routes/index.routes';

const app = express();
app.use(cors());

app.use(requestIp.mw());

app.use(logger);
app.use(limiter);

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.use(rootMiddleware);
app.use('/api/v1', router);

app.get('/', (req, res) => {
  // res.send('server is running');
  res.json({ message: 'Server is running' });
});

app.use(notFoundHandler);
app.use(errorHandler);

export default app;
