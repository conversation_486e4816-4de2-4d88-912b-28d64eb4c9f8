import jwt from 'jsonwebtoken';
import config from '../../config';
import { sendLoginOtpEmail } from '../../mail/mail.helper';
import crypto from 'crypto';
import { updateOtp } from '../../services/userService';
import { sendSMS } from '../../helper/twilioHelper';

export const createJWT = (payload: Object) => {
  return jwt.sign({ ...payload }, config.SECRET!, { expiresIn: '2h' });
};

export const generateOtp = (length: number = 6) => {
  var chars = '0123456789';
  var rnd = crypto.randomBytes(length),
    value = new Array(length),
    len = chars.length;
  for (var i = 0; i < length; i++) {
    value[i] = chars[rnd[i] % len];
  }
  return value.join('');
};

export const sendOtp = async (
  userId: number,
  phoneNumber?: string | null,
  email?: string | null,
  firstname?: string | null
) => {
  const newOtp = generateOtp(6);

  if (email && firstname) {
    await sendLoginOtpEmail(email, firstname, newOtp.toString());
    const messageData =
    'Hello ##FIRST_NAME## you OTP to Access provider chat is  ##OTP_CODE## \n\n -- Ola Digital Health Team';
  var messageText = messageData
    .replace('##FIRST_NAME##', firstname)
    .replace('##OTP_CODE##', newOtp);
    sendSMS(phoneNumber, messageText);
    await updateOtp(userId, newOtp.toString());
  }
};
