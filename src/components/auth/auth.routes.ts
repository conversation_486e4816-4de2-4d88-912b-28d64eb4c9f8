import { Router } from 'express';
import {
  authSendotp,
  authVerifyOtp,
  authSendOtpByRoleAndEmail,
  authSendOtpByRoleEmail,
  authVerifyOtpByEmail,
  getPatientOrderList
} from './auth.controller';
import { authMiddleware } from '../../middelware/auth.middleware';

const router = Router();
router.route('/send-otp').post(authSendotp);
router.route('/send-otp-by-role-email').post(authSendOtpByRoleAndEmail);
router.route('/send-otp-role-email').post(authSendOtpByRoleEmail);
router.route('/verify-otp').post(authVerifyOtp);
router.route('/verify-otp-by-email').post(authVerifyOtpByEmail);
router.route('/patient/orders').get(authMiddleware, getPatientOrderList);
// router.route("/resend-otp").post(authResendOtp);

export default router;
