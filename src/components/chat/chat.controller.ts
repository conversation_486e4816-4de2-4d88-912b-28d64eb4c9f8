import ApiResponse from '../../helper/ApiResponse';
import asyncHandler from '../../helper/asyncHandler';
import { Request, Response } from 'express';
import { chatMessageByRoomId } from '../../services/chatMessageService';
import { getUserByRoomId } from '../../services/userService';
import {
  getChatRoomByIdentifier,
  getChatRoomWithServiceByIdentifier
} from '../../services/chatRoomService';
import ApiError from '../../helper/ApiError';
import { db } from '../../db';
import { chat_messages, chat_room_members, chat_rooms } from '../../db/schema';
import { and, eq, ne, sql } from 'drizzle-orm';
import { alias } from 'drizzle-orm/pg-core';

export const getChatMessage = asyncHandler(
  async (req: Request, res: Response) => {
    const roomIdentifier = req.query.roomId;
    const room = await getChatRoomByIdentifier(roomIdentifier as string);
    const messages = await chatMessageByRoomId(room.id);
    const patient = await getUserByRoomId(room.id, 'USER');
    const provider = await getUserByRoomId(room.id, 'BUSER');

    if (req.user.user_id !== patient?.user_id) {
      throw new ApiError(400, 'Wrong Chat Room');
    }

    return res
      .status(200)
      .json(
        new ApiResponse(200, { messages, patient, provider, room }, 'Success')
      );
  }
);

export const geţChatMetaData = asyncHandler(
  async (req: Request, res: Response) => {
    const roomIdentifier = req.params.roomId;
    const roomWithService = await getChatRoomWithServiceByIdentifier(
      roomIdentifier as string
    );

    if (!roomWithService) {
      return res.status(404).json(new ApiResponse(404, null, 'Room not found'));
    }

    const patient = await getUserByRoomId(roomWithService.id, 'USER');
    const provider = await getUserByRoomId(roomWithService.id, 'BUSER');
    const currentUser = req.user?.user_id;
    const sender = currentUser === patient?.user_id ? patient : provider;

    // Extract service information
    const serviceInfo = roomWithService.service
      ? {
          service_key: roomWithService.service.service_key,
          service_name:
            roomWithService.service.display_service_name ||
            roomWithService.service.service_name,
          display_service_name: roomWithService.service.display_service_name,
          service_type: roomWithService.service.service_type,
          service_mode: roomWithService.service.service_mode,
          amount: roomWithService.service.amount
        }
      : null;

    return res.status(200).json(
      new ApiResponse(
        200,
        {
          sender,
          patient,
          provider,
          room: roomWithService,
          service: serviceInfo
        },
        'Success'
      )
    );
  }
);

export const getChatMessagesByRoom = asyncHandler(
  async (req: Request, res: Response) => {
    const roomIdentifier = req.params.roomId;
    const roomWithService = await getChatRoomWithServiceByIdentifier(
      roomIdentifier as string
    );

    if (!roomWithService) {
      return res.status(404).json(new ApiResponse(404, null, 'Room not found'));
    }

    const messages = await chatMessageByRoomId(roomWithService.id);
    const patient = await getUserByRoomId(roomWithService.id, 'USER');
    const provider = await getUserByRoomId(roomWithService.id, 'BUSER');

    // Extract service information
    const serviceInfo = roomWithService.service
      ? {
          service_key: roomWithService.service.service_key,
          service_name:
            roomWithService.service.display_service_name ||
            roomWithService.service.service_name,
          display_service_name: roomWithService.service.display_service_name,
          service_type: roomWithService.service.service_type,
          service_mode: roomWithService.service.service_mode,
          amount: roomWithService.service.amount
        }
      : null;

    // Chat metadata with participants and service details
    const chatMetadata = {
      room: {
        id: roomWithService.id,
        room_identifier: roomWithService.room_identifier,
        room_name: roomWithService.room_name,
        description: roomWithService.description,
        active: roomWithService.active
      },
      participants: {
        patient: patient
          ? {
              user_id: patient.user_id,
              user_guid: patient.user_guid,
              first_name: patient.first_name,
              last_name: patient.last_name,
              email: patient.email,
              phone: patient.phone,
              role: 'USER'
            }
          : null,
        provider: provider
          ? {
              user_id: provider.user_id,
              user_guid: provider.user_guid,
              first_name: provider.first_name,
              last_name: provider.last_name,
              email: provider.email,
              phone: provider.phone,
              role: 'BUSER'
            }
          : null
      },
      service: serviceInfo
    };

    return res.status(200).json(
      new ApiResponse(
        200,
        {
          messages,
          chatMetadata
        },
        'Success'
      )
    );
  }
);

export const readAllChatMessages = asyncHandler(
  async (req: Request, res: Response) => {
    const roomIdentifier = req.params.roomId;
    const userId = req.user.user_id;
    const room = await getChatRoomByIdentifier(roomIdentifier as string);
    await db
      .update(chat_messages)
      .set({
        read_by: sql`CASE 
                     WHEN ${userId} = ANY(${chat_messages.read_by}) 
                     THEN ${chat_messages.read_by} 
                     ELSE array_append(${chat_messages.read_by}, ${userId}) 
                   END`
      })
      .where(eq(chat_messages.room_id, room.id));
    return res.status(200).json(new ApiResponse(200, {}, 'Success'));
  }
);

export const getCountUnreadMessages = asyncHandler(
  async (req: Request, res: Response) => {
    const roomIdentifier = req.params.roomId;
    const userId = req.user.user_id;
    const room = await getChatRoomByIdentifier(roomIdentifier as string);
    const count = await db
      .select({ count: sql<number>`count(*)` })
      .from(chat_messages)
      .where(
        and(
          eq(chat_messages.room_id, room.id),
          eq(chat_messages.read_by, sql`array[${userId}]`)
        )
      );
    return res.status(200).json(new ApiResponse(200, { count }, 'Success'));
  }
);

export const checkRoomAndGetUnreadMessageCount = asyncHandler(
  async (req: Request, res: Response) => {
    const { service_key, patient_id, provider_id } = req.body;

    const userId = req.user.user_id;

    const patient = alias(chat_room_members, 'patient');
    const provider = alias(chat_room_members, 'provider');

    const isRoomExists = await db
      .select()
      .from(chat_rooms)
      .innerJoin(patient, eq(chat_rooms.id, patient.room_id))
      .innerJoin(provider, eq(chat_rooms.id, provider.room_id))
      .where(
        and(
          eq(chat_rooms.service_key, service_key),
          eq(patient.user_id, patient_id),
          eq(provider.user_id, provider_id)
        )
      );

    let unreadMessageCount = 0;

    if (isRoomExists?.length > 0) {
      const roomId = isRoomExists[0]?.chat_rooms?.id || null;
      if (roomId) {
        const count = await db
          .select({ count: sql<number>`count(*)` })
          .from(chat_messages)
          .where(
            and(
              eq(chat_messages.room_id, roomId),
              ne(chat_messages.sender_id, userId),
              sql`${userId} != ALL(${chat_messages.read_by})`
            )
          );
        unreadMessageCount = count[0]?.count || 0;
      }
    }

    return res
      .status(200)
      .json(new ApiResponse(200, { count: unreadMessageCount }, 'Success'));
  }
);
