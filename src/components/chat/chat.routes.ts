import { Router } from "express";
import { getChatMessage, geţChatMetaData, getChatMessagesByRoom, readAllChatMessages, checkRoomAndGetUnreadMessageCount } from "./chat.controller";

import { authMiddleware } from "../../middelware/auth.middleware";


const router = Router();

router.use(authMiddleware);
router.route("/").get(getChatMessage);
router.route("/unread-message-count").post(checkRoomAndGetUnreadMessageCount);
router.route("/:roomId/metadata").get(geţChatMetaData);
router.route("/:roomId/messages").get(getChatMessagesByRoom);
router.route("/:roomId/read-all-messages").get(readAllChatMessages);

export default router;
