import { NextFunction, Request, Response } from 'express';
import { getSignedUrl, upload } from './file.helper';

import { ParsedQs } from 'qs';
import { createFile } from '../../services/fileService';
import ApiResponse from '../../helper/ApiResponse';

interface FileType {
  location: string;
  mimetype: string;
  originalname: string;
  key: string;
}

export const uploadHandler = async (
  req: Request<any>,
  res: Response,
  next: NextFunction
) => {
  try {
    upload(req, res, async (err) => {
      if (err) {
        return res.status(200).json({
          status: 500,
          message: err // 'Internal Server error',
        });
      } else {
        if (!req.file) {
          return res.status(200).json({
            status: 400,
            message: 'Bad request'
          });
        }

        const file: FileType = JSON.parse(JSON.stringify(req.file));
        // const signedUrl = getSignedUrl(file.key as string);
        const params = req.body as ParsedQs;
        const file_id = await createFile(
          parseInt(req.body.user_id?.toString() as string),
          req.file?.originalname as string,
          file.key as string,
          parseInt(req.body.room_id)
        );
        return res.status(200).json({
          status: 200,
          uploadLink: file.key,
          fileUrl: file.key,
          fileId: file_id[0].insertedId
        });
      }
    });
  } catch (error) {
    next(error);
  }
};

export const getSignedUrlHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const url = getSignedUrl(req.body.file_id);
    return res
    .status(200)
    .json(
      new ApiResponse(200,  {url}, 'Success')
    );
  } catch (error) {
    next(error);
  }
};