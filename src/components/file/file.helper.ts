import AWS from 'aws-sdk';
const { S3Client } = require('@aws-sdk/client-s3');
import multerS3 from 'multer-s3';
import multer from 'multer';
import fs from 'fs';

export const generateFileName = (originalName: string) => {
  const fileNameArr = originalName.split('.');
  const ext = fileNameArr[fileNameArr.length - 1];
  const fileName =
    fileNameArr.slice(0, fileNameArr.length - 1).join('.') +
    new Date().getTime();
  return fileName + '.' + ext;
};

const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_S3_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_S3_SECRET_ACCESS_KEY,
});

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_S3_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_S3_SECRET_ACCESS_KEY,
  },
});

const storageS3 = multerS3({
  s3: s3Client,
  bucket: process.env.AWS_BUCKET_NAME ?? '',
  key: (req, file: { originalname: string }, cb: (arg0: null, arg1: string) => void) => {
    cb(
      null,
      "chat_module" + '/' + generateFileName(file.originalname),
    );
  },
  metadata: (req, file, cb) => {
    cb(null, { fieldName: file.fieldname, contentType: file.mimetype });
  }
});

const upload = multer({
  storage: storageS3,
}).single('file');

const multipleUpload = multer({
  storage: storageS3,
}).array('files', 6);

const getSignedUrl = (key: string, method?: string) =>
  s3.getSignedUrl(method ?? 'getObject', {
    Bucket: process.env.AWS_BUCKET_NAME ?? '',
    Key: key,
    Expires: 604800,
  });

const fileUpload = async (
  path: string,
  name: string,
  oldFile?: string,
  id?: string,
) => {
  const fileData = fs.readFileSync(path);
  const fileName = generateFileName(name);
  const params = {
    Bucket: process.env.AWS_BUCKET_NAME ?? '',
    Key: `${id ? id + '/' : ''}${fileName}`,
    Body: fileData,
    ContentType: getContentType(name), // Set the content type based on the file extension
    ContentDisposition: 'inline', // Set the disposition type to inline
  };
  console.log(params);
  if (oldFile) {
    const oldNameArr = oldFile?.split('/');
    let oldName = oldNameArr[oldNameArr.length - 1];
    oldName = oldName.split('?')[0];
    s3.deleteObject(
      {
        Bucket: process.env.AWS_BUCKET_NAME ?? '',
        Key: `${id ? id + '/' : ''}${oldName}`,
      },
      (err, data) => {
        if (err) {
          throw new Error(err.message);
        } else {
          console.log(data, 'Old file was deleted successfully');
        }
      },
    );
  }
  const { Location } = await s3.upload(params).promise();
  const signedUrl = getSignedUrl(`${id ? id + '/' : ''}${fileName}`);
  fs.unlinkSync(path);
  return { Location, signedUrl };
};

export { s3, upload, multipleUpload, fileUpload, getSignedUrl };

// Helper function to get the content type based on the file extension
const getContentType = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase();
  switch (ext) {
    case 'pdf':
      return 'application/pdf';
    case 'doc':
    case 'docx':
      return 'application/msword';
    case 'xls':
    case 'xlsx':
      return 'application/vnd.ms-excel';
    case 'ppt':
    case 'pptx':
      return 'application/vnd.ms-powerpoint';
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    default:
      return 'application/octet-stream';
  }
};