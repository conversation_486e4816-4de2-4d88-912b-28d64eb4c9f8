import { Request, Response } from 'express';
import ApiResponse from '../../helper/ApiResponse';
import asyncHandler from '../../helper/asyncHandler';
import { db } from '../../db';
import {
  chat_messages,
  chat_room_members,
  chat_rooms,
  telehealth_service_order,
  telehealth_services,
  users
} from '../../db/schema';
import { and, asc, desc, eq, ne, sql } from 'drizzle-orm';
import { alias } from 'drizzle-orm/pg-core';
import { decryptField } from '../../db/custom/pgcrypto';
import config from '../../config';

export const getProviderNotificationList = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.user.user_id;

    const page = Number(req.query.page || 0);
    const limit = Number(req.query.page_size || 10);
    const offset = (page - 1) * limit;

    const patientMember = alias(chat_room_members, 'patientMember');
    const providerMember = alias(chat_room_members, 'providerMember');

    const patient = alias(users, 'patient');
    const provider = alias(users, 'provider');

    const roomsResults = await db
      .selectDistinctOn([chat_rooms.id], {
        id: chat_rooms.id,
        name: chat_rooms.room_name,
        service_key: chat_rooms.service_key,
        room_identifier: chat_rooms.room_identifier,
        last_message_at: chat_rooms.last_message_at,
        patient: {
          patient_id: patient.user_id,
          patient_guid: patient.user_guid,
          first_name: await decryptField(patient.first_name),
          email: patient.email_2 ? patient.email_2 : patient.email,
          email_2: patient.email_2,
          last_name: await decryptField(patient.last_name),
          role: patient.role
        },
        provider: {
          provider_id: provider.user_id,
          provider_guid: provider.user_guid,
          first_name: await decryptField(provider.first_name),
          email: provider.email,
          last_name: await decryptField(provider.last_name),
          role: provider.role
        },
        telehealth_services: {
          service_name: telehealth_services.service_name,
          description: telehealth_services.description
        }
      })
      .from(chat_rooms)
      .leftJoin(
        telehealth_services,
        eq(chat_rooms.service_key, telehealth_services.service_key)
      )
      .leftJoin(
        telehealth_service_order,
        and(
          eq(telehealth_service_order.service_id, telehealth_services.id),
          eq(telehealth_service_order.provider_id, userId),
          eq(telehealth_service_order.status, 'accept')
        )
      )
      .innerJoin(patientMember, eq(chat_rooms.id, patientMember.room_id))
      .innerJoin(
        patient,
        and(
          eq(patient.user_id, patientMember.user_id),
          eq(patient.role, 'USER')
        )
      )
      .innerJoin(providerMember, eq(chat_rooms.id, providerMember.room_id))
      .innerJoin(
        provider,
        and(
          eq(provider.user_id, providerMember.user_id),
          eq(provider.role, 'BUSER'),
          eq(provider.user_id, userId)
        )
      )
      .leftJoin(chat_messages, eq(chat_rooms.id, chat_messages.room_id))
      .orderBy(chat_rooms.id, desc(chat_rooms.last_message_at))
      .offset(offset)
      .limit(limit);

    const totalRooms = await await db
      .selectDistinctOn([chat_rooms.id], {
        id: chat_rooms.id
      })
      .from(chat_rooms)
      .leftJoin(
        telehealth_services,
        eq(chat_rooms.service_key, telehealth_services.service_key)
      )
      .innerJoin(patientMember, eq(chat_rooms.id, patientMember.room_id))
      .innerJoin(
        patient,
        and(
          eq(patient.user_id, patientMember.user_id),
          eq(patient.role, 'USER')
        )
      )
      .innerJoin(providerMember, eq(chat_rooms.id, providerMember.room_id))
      .innerJoin(
        provider,
        and(
          eq(provider.user_id, providerMember.user_id),
          eq(provider.role, 'BUSER'),
          eq(provider.user_id, userId)
        )
      )
      .leftJoin(chat_messages, eq(chat_rooms.id, chat_messages.room_id));

    const chatRooms = await Promise.all(
      roomsResults.map(async (room) => {
        const { ...rest } = room;
        const count = await db
          .select({ count: sql<number>`count(*)` })
          .from(chat_messages)
          .where(
            and(
              ne(chat_messages.sender_id, userId),
              eq(chat_messages.room_id, room.id),
              ne(chat_messages.read_by, sql`array[${userId}]`)
            )
          );
        return {
          ...rest,
          chat_link: `${config.WEBSITE_URL}/api/auth/provider-redirect?room=${rest.room_identifier}`,
          unreadCount: count[0]?.count || 0
        };
      })
    );

    return res
      .status(200)
      .json(
        new ApiResponse(
          200,
          { chatRooms, totalRooms: totalRooms.length },
          'Success'
        )
      );
  }
);

export const getUnreadChatCount = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.user.user_id;

    const countDistinctRooms = await db
      .select({
        count: sql<number>`count(DISTINCT ${chat_messages.room_id})`
      })
      .from(chat_messages)
      .where(sql`NOT ${chat_messages.read_by} @> ARRAY[${userId}]::text[]`);

    const count = countDistinctRooms[0]?.count || '0';

    return res.status(200).json(new ApiResponse(200, count, 'Success'));
  }
);

export const getChatConsults = asyncHandler(
  async (req: Request, res: Response) => {
    const { service_key, patient_id, provider_id, room_identifier } = req.body;

    const patient = alias(users, 'patient');
    const provider = alias(users, 'provider');

    const consuls = await db
      .select({
        id: telehealth_service_order.id,
        service_id: telehealth_service_order.service_id,
        answer_given_by: telehealth_service_order.answer_given_by,
        provider_id: telehealth_service_order.provider_id,
        created_at: telehealth_service_order.created_at,
        updated_at: telehealth_service_order.updated_at,
        order_guid: telehealth_service_order.order_guid,
        status: telehealth_service_order.status,
        telehealth_services: {
          id: telehealth_services.id,
          service_name: telehealth_services.service_name,
          description: telehealth_services.description,
          service_key: telehealth_services.service_key
        },
        patient: {
          user_id: patient.user_id,
          user_guid: patient.user_guid,
          role: patient.role,
          first_name: await decryptField(patient.first_name),
          last_name: await decryptField(patient.last_name),
          email: patient.email_2 ? patient.email_2 : patient.email,
          phone: patient.phone
        },
        provider: {
          user_id: provider.user_id,
          user_guid: provider.user_guid,
          role: provider.role,
          first_name: await decryptField(provider.first_name),
          last_name: await decryptField(provider.last_name),
          email: provider.email_2 ? provider.email_2 : provider.email,
          phone: provider.phone
        }
      })
      .from(telehealth_service_order)
      .innerJoin(
        telehealth_services,
        eq(telehealth_service_order.service_id, telehealth_services.id)
      )
      .innerJoin(
        patient,
        eq(telehealth_service_order.answer_given_by, patient.user_id)
      )
      .innerJoin(
        provider,
        eq(telehealth_service_order.provider_id, provider.user_id)
      )
      .where(
        and(
          eq(telehealth_service_order.provider_id, provider_id),
          eq(telehealth_service_order.status, 'accept'),
          eq(telehealth_services.service_key, service_key),
          eq(telehealth_service_order.answer_given_by, patient_id)
        )
      );

    // const structuredConsuls = consuls.map(
    //   ({ telehealth_service_order, telehealth_services }) => ({
    //     ...telehealth_service_order,
    //     telehealth_services: { ...telehealth_services }
    //   })
    // );

    return res.status(200).json(new ApiResponse(200, consuls, 'Success'));
  }
);
