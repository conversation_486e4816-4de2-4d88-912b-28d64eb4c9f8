import { Router } from 'express';
import {
  getChatConsults,
  getProviderNotificationList,
  getUnreadChatCount
} from './notification.controller';
import { authMiddleware } from '../../middelware/auth.middleware';

const router = Router();

router.use(authMiddleware);
router.route('/').get(getProviderNotificationList);
router.route('/get-count').get(getUnreadChatCount);
router.route('/get-consults').post(getChatConsults);

export default router;
