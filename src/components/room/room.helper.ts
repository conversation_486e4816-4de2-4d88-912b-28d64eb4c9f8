import { sendSMS } from '../../helper/twilioHelper';

export const sendSMSMessage = (
  phoneNumber: any,
  patientName: any,
  chatLink: any
) => {
  const messageData =
    'Hello ##PATIENT_NAME## You have received an important message from your healthcare provider. Please use the below provided link to view the message: ##LINK## \n\n -- Ola Digital Health Team';
  var messageText = messageData
    .replace('##PATIENT_NAME##', patientName)
    .replace('##LINK##', chatLink);

  console.log(messageText);

  sendSMS(phoneNumber, messageText);
};
