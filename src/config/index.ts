const config = {
  SERVER_PORT: process.env.SERVER_PORT || 5001,
  DATABASE_URL:
    process.env.DATABASE_URL ||
    'postgres://postgres:postgres@localhost:5432/chatportal',
  SOCKET_PORT: process.env.SOCKET_PORT || 5002,
  RADIS_SERVER: process.env.RADIS_SERVER || 'localhost',
  RADIS_PORT: Number(process.env.RADIS_PORT) || 6379,
  PG_ENCRYPTION_KEY: process.env.PG_ENCRYPTION_KEY || '',
  SECRET: process.env.SECRET || 'secret',
  MAILCHIMP_API_KEY: process.env.MAILCHIMP_API_KEY || '',
  MAILCHIMP_SENDER_EMAIL: process.env.MAILCHIMP_SENDER_EMAIL || '<EMAIL>',
  TWILIO_ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID || "**********************************",
  TWILIO_AUTHTOKEN: process.env.TWILIO_AUTHTOKEN || "b3f969867d290be7d65fad2788dcb4b9",
  TWILIO_PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER || "+***********",
  TWILIO_DEFAULT_PHONE_NUMBER: process.env.TWILIO_DEFAULT_PHONE_NUMBER || "+***********",
  TWILIO_INVITATION_TEXT_NUMBERS: process.env.TWILIO_INVITATION_TEXT_NUMBERS || "+***********",
  TWILIO_DEFAULT_OTP: process.env.TWILIO_DEFAULT_OTP || "123456",
  TWILIO_MESSAGE_SID: process.env.TWILIO_MESSAGE_SID,
  WEBSITE_URL : process.env.WEBSITE_URL || "http://localhost:3000",
  MAILCHIMP_FROM_NAME: process.env.MAILCHIMP_FROM_NAME || 'OLA MD Team',
}

export default config;
