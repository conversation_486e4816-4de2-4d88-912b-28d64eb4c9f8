-- DO $$ BEGIN
--  CREATE TYPE "public"."user_gender" AS ENUM('male', 'female', 'others', 'transgender-male', 'transgender-female');
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  CREATE TYPE "public"."user_role" AS ENUM('USER', 'BUSER', 'AUSER', 'medical_assistant', 'viewer', 'support_user', 'pharmacist', 'PHARMACY', 'GROUP_ADMIN', 'SUPPORT_ADMIN');
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  CREATE TYPE "public"."user_status" AS ENUM('AVAILABLE', 'BUSY', 'AWAY', 'OFFLINE', 'ACTIVATION_PENDING', 'ONBOARDING_PENDING', 'PROFILE_INCOMPLETE');
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  CREATE TYPE "public"."user_sub_role" AS ENUM('USER', 'DIETICIAN_NUTRITION', 'MENTAL_HEALTH', 'WEIGHT_LOSS_MANAGEMENT', 'DIABETES_PREVENTION');
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "chat_messages" (
	"id" serial PRIMARY KEY NOT NULL,
	"message" text,
	"sender_id" serial NOT NULL,
	"room_id" serial NOT NULL,
	"deleted" boolean DEFAULT false NOT NULL,
	"deleted_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "chat_room_members" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" serial NOT NULL,
	"room_id" serial NOT NULL,
	"deleted" boolean DEFAULT false NOT NULL,
	"deleted_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "chat_rooms" (
	"id" serial PRIMARY KEY NOT NULL,
	"room_name" text,
	"room_identifier" uuid DEFAULT gen_random_uuid(),
	"deleted" boolean DEFAULT false NOT NULL,
	"deleted_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
-- --> statement-breakpoint
-- CREATE TABLE IF NOT EXISTS "tennant_master" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"tennant_name" text,
-- 	"support_email" text,
-- 	"filter_display" boolean DEFAULT false NOT NULL,
-- 	"parent_id" serial NOT NULL,
-- 	"access_lab_client_number" text,
-- 	"mailchip_from_email" text,
-- 	"mailchip_from_email_name" text,
-- 	"paypal_client_id" text,
-- 	"paypal_client_secret" text,
-- 	"stripe_client_id" text,
-- 	"stripe_client_secret" text,
-- 	"preferred_payment_gateway" text,
-- 	"twilio_account_sid" text,
-- 	"twilio_auth_token" text,
-- 	"twilio_phone_number" text,
-- 	"tennant_display_name" text,
-- 	"dosespot_client_id" text,
-- 	"dosespot_client_secret" text,
-- 	"deleted" boolean DEFAULT false NOT NULL,
-- 	"deleted_at" timestamp,
-- 	"created_at" timestamp DEFAULT now() NOT NULL,
-- 	"updated_at" timestamp DEFAULT now() NOT NULL
-- );
-- --> statement-breakpoint
-- CREATE TABLE IF NOT EXISTS "users" (
-- 	"user_id" serial PRIMARY KEY NOT NULL,
-- 	"user_guid" uuid DEFAULT gen_random_uuid(),
-- 	"password" text,
-- 	"role" "user_role" DEFAULT 'USER' NOT NULL,
-- 	"status" "user_status" DEFAULT 'OFFLINE' NOT NULL,
-- 	"install_type" text,
-- 	"first_name" text,
-- 	"last_name" text,
-- 	"email" text,
-- 	"email_2" text,
-- 	"phone" text,
-- 	"secondary_phone" text,
-- 	"gender" "user_gender",
-- 	"user_avatar" text,
-- 	"dob" text,
-- 	"otp" text,
-- 	"last_active" timestamp,
-- 	"rxPersonId" text,
-- 	"rxStatus" text,
-- 	"dosespot_api_response" text,
-- 	"sub_role" "user_sub_role" DEFAULT 'USER' NOT NULL,
-- 	"tennant_id" serial NOT NULL,
-- 	"is_tennant_owner" boolean DEFAULT false NOT NULL,
-- 	"app_timezone" json,
-- 	"tenant_access" text,
-- 	"deleted" boolean DEFAULT false NOT NULL,
-- 	"created_at" timestamp DEFAULT now() NOT NULL,
-- 	"updated_at" timestamp DEFAULT now() NOT NULL
-- );
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "chat_messages" ADD CONSTRAINT "chat_messages_sender_id_users_user_id_fk" FOREIGN KEY ("sender_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "chat_messages" ADD CONSTRAINT "chat_messages_room_id_chat_rooms_id_fk" FOREIGN KEY ("room_id") REFERENCES "public"."chat_rooms"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "chat_room_members" ADD CONSTRAINT "chat_room_members_user_id_users_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "chat_room_members" ADD CONSTRAINT "chat_room_members_room_id_chat_rooms_id_fk" FOREIGN KEY ("room_id") REFERENCES "public"."chat_rooms"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
