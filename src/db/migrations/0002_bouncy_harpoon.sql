CREATE TABLE IF NOT EXISTS "chat_files" (
	"file_id" serial PRIMARY KEY NOT NULL,
	"user_id" serial NOT NULL,
	"room_id" serial NOT NULL,
	"name" text,
	"path" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
-- --> statement-breakpoint
-- CREATE TABLE IF NOT EXISTS "user_files" (
-- 	"user_file_id" serial PRIMARY KEY NOT NULL,
-- 	"user_id" serial NOT NULL,
-- 	"created_by" serial NOT NULL,
-- 	"doctor_id" serial NOT NULL,
-- 	"name" text,
-- 	"path" text,
-- 	"order_id" serial NOT NULL,
-- 	"created_at" timestamp DEFAULT now() NOT NULL,
-- 	"updated_at" timestamp DEFAULT now() NOT NULL
-- );
--> statement-breakpoint
ALTER TABLE "chat_messages" ADD COLUMN "file_id" serial NOT NULL;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "chat_files" ADD CONSTRAINT "chat_files_user_id_users_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "chat_files" ADD CONSTRAINT "chat_files_room_id_chat_rooms_id_fk" FOREIGN KEY ("room_id") REFERENCES "public"."chat_rooms"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
-- DO $$ BEGIN
--  ALTER TABLE "user_files" ADD CONSTRAINT "user_files_user_id_users_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
--> statement-breakpoint
-- DO $$ BEGIN
--  ALTER TABLE "user_files" ADD CONSTRAINT "user_files_created_by_users_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
--> statement-breakpoint
-- DO $$ BEGIN
--  ALTER TABLE "user_files" ADD CONSTRAINT "user_files_doctor_id_users_user_id_fk" FOREIGN KEY ("doctor_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
