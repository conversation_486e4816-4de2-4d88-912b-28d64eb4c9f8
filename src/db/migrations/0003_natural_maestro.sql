-- DO $$ BEGIN
--  CREATE TYPE "public"."enum_orders_category" AS ENUM('CCM', 'RPM', 'BHI');
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  CREATE TYPE "public"."enum_orders_status" AS ENUM('STARTED', 'INPROGRESS', 'ENDED', 'NOANSWER', 'REJECTED', 'QUEUED', 'RINGING', 'CANCELLED', 'COMPLETED', 'BUSY', 'FAILED');
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  CREATE TYPE "public"."enum_orders_type" AS ENUM('audio', 'video', 'one-way', 'online', 'voip');
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  CREATE TYPE "public"."enum_telehealth_service_order_claim_type" AS ENUM('AUTO', 'WORK');
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  CREATE TYPE "public"."enum_telehealth_service_order_service_type" AS ENUM('SYNC', 'ASYNC');
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  CREATE TYPE "public"."enum_telehealth_service_order_status" AS ENUM('pending', 'accept', 'completed', 'errored', 'cancelled', 'patient_verification_pending', 'archive', 'cancelled_by_provider', 'LabRequested', 'LabReceived', 'schedule_pending', 'lab_approval_pending', 'lab_results_approved', 'lab_results_denied', 'clinical_denial');
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  CREATE TYPE "public"."enum_telehealth_service_order_visit_type" AS ENUM('IN_PERSON', 'ONLINE');
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  CREATE TYPE "public"."enum_telehealth_services_service_mode" AS ENUM('SYNC', 'ASYNC', 'BOTH_SYNC_ASYNC');
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  CREATE TYPE "public"."enum_telehealth_services_service_type" AS ENUM('BUSER', 'AUSER', 'medical_assistant', 'pharmacist', 'DIETICIAN_NUTRITION', 'MENTAL_HEALTH', 'WEIGHT_LOSS_MANAGEMENT', 'DIABETES_PREVENTION');
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- CREATE TABLE IF NOT EXISTS "orders" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"order_id" text NOT NULL,
-- 	"start_time" timestamp with time zone,
-- 	"end_time" timestamp with time zone,
-- 	"caller_id" integer,
-- 	"callee_id" integer,
-- 	"doctor_id" integer,
-- 	"status" "enum_orders_status",
-- 	"type" "enum_orders_type",
-- 	"category" "enum_orders_category",
-- 	"conversation_mode" text,
-- 	"cost" numeric,
-- 	"billed" boolean DEFAULT false,
-- 	"caller_location" varchar(255),
-- 	"callee_location" varchar(255),
-- 	"instructions" text,
-- 	"diagnosis" text,
-- 	"procedure" text,
-- 	"visit_summary" text,
-- 	"regenerate_visit_summary" boolean DEFAULT false,
-- 	"regenerate_ccd_file" boolean DEFAULT false,
-- 	"is_virtual_room" boolean DEFAULT false,
-- 	"hide_visit_details" boolean DEFAULT false,
-- 	"audio_stream_screenshot" text,
-- 	"created_at" timestamp with time zone,
-- 	"updated_at" timestamp with time zone,
-- 	"currency" text,
-- 	"order_guid" varchar(50),
-- 	"duration" varchar(50),
-- 	CONSTRAINT "orders_order_id_key" UNIQUE("order_id")
-- );
-- --> statement-breakpoint
-- CREATE TABLE IF NOT EXISTS "telehealth_service_order" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"service_id" integer,
-- 	"answer_given_by" integer,
-- 	"provider_id" integer,
-- 	"order_id" integer,
-- 	"created_at" timestamp with time zone,
-- 	"updated_at" timestamp with time zone,
-- 	"order_guid" varchar(50),
-- 	"status" "enum_telehealth_service_order_status",
-- 	"prescription_delivery" boolean DEFAULT false,
-- 	"ravkoo_prescription_option" boolean DEFAULT false,
-- 	"pharmacy_name" varchar(100) DEFAULT '',
-- 	"pharmacy_phone" varchar(20) DEFAULT '',
-- 	"pharmacy_address" varchar(255) DEFAULT '',
-- 	"service_type" "enum_telehealth_service_order_service_type" DEFAULT 'SYNC',
-- 	"release_medical" boolean DEFAULT false,
-- 	"release_medical_at" timestamp with time zone,
-- 	"is_cancelled_by_provider" boolean DEFAULT false,
-- 	"cancelled_at" timestamp with time zone,
-- 	"pharmacy_city" varchar(20),
-- 	"pharmacy_state" varchar(20),
-- 	"pharmacy_zip" varchar(20),
-- 	"pharmacy_fax" varchar(20),
-- 	"pharmacy_preference" integer DEFAULT 0 NOT NULL,
-- 	"claim_type" "enum_telehealth_service_order_claim_type",
-- 	"claim_id" varchar(255),
-- 	"payor_name" varchar(255),
-- 	"injury_date" timestamp with time zone,
-- 	"current_medicines" varchar(255),
-- 	"allergies_to_medicines" varchar(255),
-- 	"other_allergies" varchar(255),
-- 	"doctor_notes" varchar(255),
-- 	"abnormal_findings" varchar(255),
-- 	"terms_and_conditions_accepted" boolean DEFAULT false,
-- 	"cancellation_reason" varchar(255),
-- 	"is_refill_request" boolean DEFAULT false,
-- 	"external_order_id" varchar(50),
-- 	"follow_up" integer,
-- 	"visit_type" "enum_telehealth_service_order_visit_type",
-- 	"completion_reason" varchar(255),
-- 	"session_type" varchar(255),
-- 	"schedule_type" varchar(255),
-- 	"affiliateid" varchar(255),
-- 	"client_name" varchar(255),
-- 	"dosespot_pharmacy_id" varchar(200),
-- 	"pharmacy_ncpdp_id" varchar(255),
-- 	"erx_prescription_visitied_at" timestamp with time zone,
-- 	CONSTRAINT "telehealth_service_order_order_guid_key" UNIQUE("order_guid")
-- );
-- --> statement-breakpoint
-- CREATE TABLE IF NOT EXISTS "telehealth_services" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"service_name" varchar(200) NOT NULL,
-- 	"description" varchar(500) NOT NULL,
-- 	"created_at" timestamp with time zone,
-- 	"updated_at" timestamp with time zone,
-- 	"service_type" "enum_telehealth_services_service_type" DEFAULT 'BUSER',
-- 	"display_order" integer DEFAULT 1,
-- 	"service_mode" "enum_telehealth_services_service_mode" DEFAULT 'BOTH_SYNC_ASYNC',
-- 	"deleted" boolean DEFAULT false,
-- 	"deleted_at" timestamp with time zone,
-- 	"amount" integer,
-- 	"tenant_id" integer,
-- 	"paypal_plan_id" text,
-- 	"title" varchar(255),
-- 	"subtitle" varchar(255),
-- 	"service_key" varchar(255),
-- 	"session_type" varchar(255),
-- 	"fields_options" varchar(255),
-- 	"on_complete_script" varchar(2000),
-- 	"on_follow_up_script" varchar(2000),
-- 	"access_labs_test_code" varchar(2000),
-- 	"on_create_script" varchar(2000),
-- 	"on_lab_result_received_script" varchar(2000),
-- 	"on_update_schedule_script" varchar(2000),
-- 	"disclaimer" text,
-- 	"service_details" text,
-- 	"is_video_call" boolean DEFAULT true NOT NULL,
-- 	"is_audio_call" boolean DEFAULT true NOT NULL,
-- 	"display_questionnaire" boolean DEFAULT false NOT NULL,
-- 	"display_service_name" text,
-- 	"erx_drug_key" varchar(255),
-- 	CONSTRAINT "telehealth_services_service_name_key" UNIQUE("service_name")
-- );
-- --> statement-breakpoint
-- DO $$ BEGIN
--  ALTER TABLE "orders" ADD CONSTRAINT "orders_caller_id_users_user_id_fk" FOREIGN KEY ("caller_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  ALTER TABLE "orders" ADD CONSTRAINT "orders_callee_id_users_user_id_fk" FOREIGN KEY ("callee_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  ALTER TABLE "orders" ADD CONSTRAINT "orders_doctor_id_users_user_id_fk" FOREIGN KEY ("doctor_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  ALTER TABLE "orders" ADD CONSTRAINT "orders_order_guid_telehealth_service_order_order_guid_fk" FOREIGN KEY ("order_guid") REFERENCES "public"."telehealth_service_order"("order_guid") ON DELETE no action ON UPDATE no action;
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  ALTER TABLE "telehealth_service_order" ADD CONSTRAINT "telehealth_service_order_service_id_telehealth_services_id_fk" FOREIGN KEY ("service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  ALTER TABLE "telehealth_service_order" ADD CONSTRAINT "telehealth_service_order_answer_given_by_users_user_id_fk" FOREIGN KEY ("answer_given_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  ALTER TABLE "telehealth_service_order" ADD CONSTRAINT "telehealth_service_order_provider_id_users_user_id_fk" FOREIGN KEY ("provider_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
-- --> statement-breakpoint
-- DO $$ BEGIN
--  ALTER TABLE "telehealth_service_order" ADD CONSTRAINT "telehealth_service_order_order_id_orders_id_fk" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE no action ON UPDATE no action;
-- EXCEPTION
--  WHEN duplicate_object THEN null;
-- END $$;
