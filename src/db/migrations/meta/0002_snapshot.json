{"id": "4118a207-80c3-48f5-bae8-4dcc569a0ee5", "prevId": "9f1e78a1-fa31-4118-9841-da1c9ad08a12", "version": "7", "dialect": "postgresql", "tables": {"public.chat_files": {"name": "chat_files", "schema": "", "columns": {"file_id": {"name": "file_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "serial", "primaryKey": false, "notNull": true}, "room_id": {"name": "room_id", "type": "serial", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_files_user_id_users_user_id_fk": {"name": "chat_files_user_id_users_user_id_fk", "tableFrom": "chat_files", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_files_room_id_chat_rooms_id_fk": {"name": "chat_files_room_id_chat_rooms_id_fk", "tableFrom": "chat_files", "tableTo": "chat_rooms", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.chat_messages": {"name": "chat_messages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "file_id": {"name": "file_id", "type": "serial", "primaryKey": false, "notNull": true}, "sender_id": {"name": "sender_id", "type": "serial", "primaryKey": false, "notNull": true}, "room_id": {"name": "room_id", "type": "serial", "primaryKey": false, "notNull": true}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_messages_sender_id_users_user_id_fk": {"name": "chat_messages_sender_id_users_user_id_fk", "tableFrom": "chat_messages", "tableTo": "users", "columnsFrom": ["sender_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_messages_room_id_chat_rooms_id_fk": {"name": "chat_messages_room_id_chat_rooms_id_fk", "tableFrom": "chat_messages", "tableTo": "chat_rooms", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.chat_room_members": {"name": "chat_room_members", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "serial", "primaryKey": false, "notNull": true}, "room_id": {"name": "room_id", "type": "serial", "primaryKey": false, "notNull": true}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_room_members_user_id_users_user_id_fk": {"name": "chat_room_members_user_id_users_user_id_fk", "tableFrom": "chat_room_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_room_members_room_id_chat_rooms_id_fk": {"name": "chat_room_members_room_id_chat_rooms_id_fk", "tableFrom": "chat_room_members", "tableTo": "chat_rooms", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.chat_rooms": {"name": "chat_rooms", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "room_name": {"name": "room_name", "type": "text", "primaryKey": false, "notNull": true}, "room_identifier": {"name": "room_identifier", "type": "uuid", "primaryKey": false, "notNull": false, "default": "gen_random_uuid()"}, "service_key": {"name": "service_key", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.tennant_master": {"name": "tennant_master", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tennant_name": {"name": "tennant_name", "type": "text", "primaryKey": false, "notNull": false}, "support_email": {"name": "support_email", "type": "text", "primaryKey": false, "notNull": false}, "filter_display": {"name": "filter_display", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "parent_id": {"name": "parent_id", "type": "serial", "primaryKey": false, "notNull": true}, "access_lab_client_number": {"name": "access_lab_client_number", "type": "text", "primaryKey": false, "notNull": false}, "mailchip_from_email": {"name": "mailchip_from_email", "type": "text", "primaryKey": false, "notNull": false}, "mailchip_from_email_name": {"name": "mailchip_from_email_name", "type": "text", "primaryKey": false, "notNull": false}, "paypal_client_id": {"name": "paypal_client_id", "type": "text", "primaryKey": false, "notNull": false}, "paypal_client_secret": {"name": "paypal_client_secret", "type": "text", "primaryKey": false, "notNull": false}, "stripe_client_id": {"name": "stripe_client_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_client_secret": {"name": "stripe_client_secret", "type": "text", "primaryKey": false, "notNull": false}, "preferred_payment_gateway": {"name": "preferred_payment_gateway", "type": "text", "primaryKey": false, "notNull": false}, "twilio_account_sid": {"name": "twilio_account_sid", "type": "text", "primaryKey": false, "notNull": false}, "twilio_auth_token": {"name": "twilio_auth_token", "type": "text", "primaryKey": false, "notNull": false}, "twilio_phone_number": {"name": "twilio_phone_number", "type": "text", "primaryKey": false, "notNull": false}, "tennant_display_name": {"name": "tennant_display_name", "type": "text", "primaryKey": false, "notNull": false}, "dosespot_client_id": {"name": "dosespot_client_id", "type": "text", "primaryKey": false, "notNull": false}, "dosespot_client_secret": {"name": "dosespot_client_secret", "type": "text", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.users": {"name": "users", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_guid": {"name": "user_guid", "type": "uuid", "primaryKey": false, "notNull": false, "default": "gen_random_uuid()"}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'USER'"}, "status": {"name": "status", "type": "user_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'OFFLINE'"}, "install_type": {"name": "install_type", "type": "text", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "email_2": {"name": "email_2", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "secondary_phone": {"name": "secondary_phone", "type": "text", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "user_gender", "typeSchema": "public", "primaryKey": false, "notNull": false}, "user_avatar": {"name": "user_avatar", "type": "text", "primaryKey": false, "notNull": false}, "dob": {"name": "dob", "type": "text", "primaryKey": false, "notNull": false}, "otp": {"name": "otp", "type": "text", "primaryKey": false, "notNull": false}, "last_active": {"name": "last_active", "type": "timestamp", "primaryKey": false, "notNull": false}, "rxPersonId": {"name": "rxPersonId", "type": "text", "primaryKey": false, "notNull": false}, "rxStatus": {"name": "rxStatus", "type": "text", "primaryKey": false, "notNull": false}, "dosespot_api_response": {"name": "dosespot_api_response", "type": "text", "primaryKey": false, "notNull": false}, "sub_role": {"name": "sub_role", "type": "user_sub_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'USER'"}, "tennant_id": {"name": "tennant_id", "type": "serial", "primaryKey": false, "notNull": true}, "is_tennant_owner": {"name": "is_tennant_owner", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "app_timezone": {"name": "app_timezone", "type": "json", "primaryKey": false, "notNull": false}, "tenant_access": {"name": "tenant_access", "type": "text", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.user_files": {"name": "user_files", "schema": "", "columns": {"user_file_id": {"name": "user_file_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "serial", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "serial", "primaryKey": false, "notNull": true}, "doctor_id": {"name": "doctor_id", "type": "serial", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": false}, "order_id": {"name": "order_id", "type": "serial", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_files_user_id_users_user_id_fk": {"name": "user_files_user_id_users_user_id_fk", "tableFrom": "user_files", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_files_created_by_users_user_id_fk": {"name": "user_files_created_by_users_user_id_fk", "tableFrom": "user_files", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_files_doctor_id_users_user_id_fk": {"name": "user_files_doctor_id_users_user_id_fk", "tableFrom": "user_files", "tableTo": "users", "columnsFrom": ["doctor_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {"public.user_gender": {"name": "user_gender", "schema": "public", "values": ["male", "female", "others", "transgender-male", "transgender-female"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["USER", "BUSER", "AUSER", "medical_assistant", "viewer", "support_user", "pharmacist", "PHARMACY", "GROUP_ADMIN", "SUPPORT_ADMIN"]}, "public.user_status": {"name": "user_status", "schema": "public", "values": ["AVAILABLE", "BUSY", "AWAY", "OFFLINE", "ACTIVATION_PENDING", "ONBOARDING_PENDING", "PROFILE_INCOMPLETE"]}, "public.user_sub_role": {"name": "user_sub_role", "schema": "public", "values": ["USER", "DIETICIAN_NUTRITION", "MENTAL_HEALTH", "WEIGHT_LOSS_MANAGEMENT", "DIABETES_PREVENTION"]}}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}