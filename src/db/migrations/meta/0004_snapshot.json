{"id": "90d51674-58af-45c2-87ba-b689c22f26f7", "prevId": "2b272fac-315f-491b-8093-c34913e27f32", "version": "7", "dialect": "postgresql", "tables": {"public.chat_files": {"name": "chat_files", "schema": "", "columns": {"file_id": {"name": "file_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "serial", "primaryKey": false, "notNull": true}, "room_id": {"name": "room_id", "type": "serial", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_files_user_id_users_user_id_fk": {"name": "chat_files_user_id_users_user_id_fk", "tableFrom": "chat_files", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_files_room_id_chat_rooms_id_fk": {"name": "chat_files_room_id_chat_rooms_id_fk", "tableFrom": "chat_files", "tableTo": "chat_rooms", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.chat_messages": {"name": "chat_messages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "file_id": {"name": "file_id", "type": "serial", "primaryKey": false, "notNull": true}, "sender_id": {"name": "sender_id", "type": "serial", "primaryKey": false, "notNull": true}, "room_id": {"name": "room_id", "type": "serial", "primaryKey": false, "notNull": true}, "read_by": {"name": "read_by", "type": "text[]", "primaryKey": false, "notNull": true, "default": "ARRAY[]::text[]"}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_messages_sender_id_users_user_id_fk": {"name": "chat_messages_sender_id_users_user_id_fk", "tableFrom": "chat_messages", "tableTo": "users", "columnsFrom": ["sender_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_messages_room_id_chat_rooms_id_fk": {"name": "chat_messages_room_id_chat_rooms_id_fk", "tableFrom": "chat_messages", "tableTo": "chat_rooms", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.chat_room_members": {"name": "chat_room_members", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "serial", "primaryKey": false, "notNull": true}, "room_id": {"name": "room_id", "type": "serial", "primaryKey": false, "notNull": true}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_room_members_user_id_users_user_id_fk": {"name": "chat_room_members_user_id_users_user_id_fk", "tableFrom": "chat_room_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_room_members_room_id_chat_rooms_id_fk": {"name": "chat_room_members_room_id_chat_rooms_id_fk", "tableFrom": "chat_room_members", "tableTo": "chat_rooms", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.chat_rooms": {"name": "chat_rooms", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "room_name": {"name": "room_name", "type": "text", "primaryKey": false, "notNull": true}, "room_identifier": {"name": "room_identifier", "type": "uuid", "primaryKey": false, "notNull": false, "default": "gen_random_uuid()"}, "service_key": {"name": "service_key", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.tennant_master": {"name": "tennant_master", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tennant_name": {"name": "tennant_name", "type": "text", "primaryKey": false, "notNull": false}, "support_email": {"name": "support_email", "type": "text", "primaryKey": false, "notNull": false}, "filter_display": {"name": "filter_display", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "parent_id": {"name": "parent_id", "type": "serial", "primaryKey": false, "notNull": true}, "access_lab_client_number": {"name": "access_lab_client_number", "type": "text", "primaryKey": false, "notNull": false}, "mailchip_from_email": {"name": "mailchip_from_email", "type": "text", "primaryKey": false, "notNull": false}, "mailchip_from_email_name": {"name": "mailchip_from_email_name", "type": "text", "primaryKey": false, "notNull": false}, "paypal_client_id": {"name": "paypal_client_id", "type": "text", "primaryKey": false, "notNull": false}, "paypal_client_secret": {"name": "paypal_client_secret", "type": "text", "primaryKey": false, "notNull": false}, "stripe_client_id": {"name": "stripe_client_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_client_secret": {"name": "stripe_client_secret", "type": "text", "primaryKey": false, "notNull": false}, "preferred_payment_gateway": {"name": "preferred_payment_gateway", "type": "text", "primaryKey": false, "notNull": false}, "twilio_account_sid": {"name": "twilio_account_sid", "type": "text", "primaryKey": false, "notNull": false}, "twilio_auth_token": {"name": "twilio_auth_token", "type": "text", "primaryKey": false, "notNull": false}, "twilio_phone_number": {"name": "twilio_phone_number", "type": "text", "primaryKey": false, "notNull": false}, "tennant_display_name": {"name": "tennant_display_name", "type": "text", "primaryKey": false, "notNull": false}, "dosespot_client_id": {"name": "dosespot_client_id", "type": "text", "primaryKey": false, "notNull": false}, "dosespot_client_secret": {"name": "dosespot_client_secret", "type": "text", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.users": {"name": "users", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_guid": {"name": "user_guid", "type": "uuid", "primaryKey": false, "notNull": false, "default": "gen_random_uuid()"}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'USER'"}, "status": {"name": "status", "type": "user_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'OFFLINE'"}, "install_type": {"name": "install_type", "type": "text", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "email_2": {"name": "email_2", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "secondary_phone": {"name": "secondary_phone", "type": "text", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "user_gender", "typeSchema": "public", "primaryKey": false, "notNull": false}, "user_avatar": {"name": "user_avatar", "type": "text", "primaryKey": false, "notNull": false}, "dob": {"name": "dob", "type": "text", "primaryKey": false, "notNull": false}, "otp": {"name": "otp", "type": "text", "primaryKey": false, "notNull": false}, "last_active": {"name": "last_active", "type": "timestamp", "primaryKey": false, "notNull": false}, "rxPersonId": {"name": "rxPersonId", "type": "text", "primaryKey": false, "notNull": false}, "rxStatus": {"name": "rxStatus", "type": "text", "primaryKey": false, "notNull": false}, "dosespot_api_response": {"name": "dosespot_api_response", "type": "text", "primaryKey": false, "notNull": false}, "sub_role": {"name": "sub_role", "type": "user_sub_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'USER'"}, "tennant_id": {"name": "tennant_id", "type": "serial", "primaryKey": false, "notNull": true}, "is_tennant_owner": {"name": "is_tennant_owner", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "app_timezone": {"name": "app_timezone", "type": "json", "primaryKey": false, "notNull": false}, "tenant_access": {"name": "tenant_access", "type": "text", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "end_time": {"name": "end_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "caller_id": {"name": "caller_id", "type": "integer", "primaryKey": false, "notNull": false}, "callee_id": {"name": "callee_id", "type": "integer", "primaryKey": false, "notNull": false}, "doctor_id": {"name": "doctor_id", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_orders_status", "typeSchema": "public", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "enum_orders_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "enum_orders_category", "typeSchema": "public", "primaryKey": false, "notNull": false}, "conversation_mode": {"name": "conversation_mode", "type": "text", "primaryKey": false, "notNull": false}, "cost": {"name": "cost", "type": "numeric", "primaryKey": false, "notNull": false}, "billed": {"name": "billed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "caller_location": {"name": "caller_location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "callee_location": {"name": "callee_location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "instructions": {"name": "instructions", "type": "text", "primaryKey": false, "notNull": false}, "diagnosis": {"name": "diagnosis", "type": "text", "primaryKey": false, "notNull": false}, "procedure": {"name": "procedure", "type": "text", "primaryKey": false, "notNull": false}, "visit_summary": {"name": "visit_summary", "type": "text", "primaryKey": false, "notNull": false}, "regenerate_visit_summary": {"name": "regenerate_visit_summary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "regenerate_ccd_file": {"name": "regenerate_ccd_file", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_virtual_room": {"name": "is_virtual_room", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "hide_visit_details": {"name": "hide_visit_details", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "audio_stream_screenshot": {"name": "audio_stream_screenshot", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "order_guid": {"name": "order_guid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"orders_caller_id_users_user_id_fk": {"name": "orders_caller_id_users_user_id_fk", "tableFrom": "orders", "tableTo": "users", "columnsFrom": ["caller_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "orders_callee_id_users_user_id_fk": {"name": "orders_callee_id_users_user_id_fk", "tableFrom": "orders", "tableTo": "users", "columnsFrom": ["callee_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "orders_doctor_id_users_user_id_fk": {"name": "orders_doctor_id_users_user_id_fk", "tableFrom": "orders", "tableTo": "users", "columnsFrom": ["doctor_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "orders_order_guid_telehealth_service_order_order_guid_fk": {"name": "orders_order_guid_telehealth_service_order_order_guid_fk", "tableFrom": "orders", "tableTo": "telehealth_service_order", "columnsFrom": ["order_guid"], "columnsTo": ["order_guid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"orders_order_id_key": {"name": "orders_order_id_key", "nullsNotDistinct": false, "columns": ["order_id"]}}}, "public.telehealth_service_order": {"name": "telehealth_service_order", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": false}, "answer_given_by": {"name": "answer_given_by", "type": "integer", "primaryKey": false, "notNull": false}, "provider_id": {"name": "provider_id", "type": "integer", "primaryKey": false, "notNull": false}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "order_guid": {"name": "order_guid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_telehealth_service_order_status", "typeSchema": "public", "primaryKey": false, "notNull": false}, "prescription_delivery": {"name": "prescription_delivery", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "ravkoo_prescription_option": {"name": "ravkoo_prescription_option", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "pharmacy_name": {"name": "pharmacy_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "''"}, "pharmacy_phone": {"name": "pharmacy_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "''"}, "pharmacy_address": {"name": "pharmacy_address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "''"}, "service_type": {"name": "service_type", "type": "enum_telehealth_service_order_service_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'SYNC'"}, "release_medical": {"name": "release_medical", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "release_medical_at": {"name": "release_medical_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_cancelled_by_provider": {"name": "is_cancelled_by_provider", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "cancelled_at": {"name": "cancelled_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "pharmacy_city": {"name": "pharmacy_city", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "pharmacy_state": {"name": "pharmacy_state", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "pharmacy_zip": {"name": "pharmacy_zip", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "pharmacy_fax": {"name": "pharmacy_fax", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "pharmacy_preference": {"name": "pharmacy_preference", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "claim_type": {"name": "claim_type", "type": "enum_telehealth_service_order_claim_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "claim_id": {"name": "claim_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "payor_name": {"name": "payor_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "injury_date": {"name": "injury_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "current_medicines": {"name": "current_medicines", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "allergies_to_medicines": {"name": "allergies_to_medicines", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "other_allergies": {"name": "other_allergies", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "doctor_notes": {"name": "doctor_notes", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "abnormal_findings": {"name": "abnormal_findings", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "terms_and_conditions_accepted": {"name": "terms_and_conditions_accepted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "cancellation_reason": {"name": "cancellation_reason", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_refill_request": {"name": "is_refill_request", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "external_order_id": {"name": "external_order_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "follow_up": {"name": "follow_up", "type": "integer", "primaryKey": false, "notNull": false}, "visit_type": {"name": "visit_type", "type": "enum_telehealth_service_order_visit_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "completion_reason": {"name": "completion_reason", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "session_type": {"name": "session_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "schedule_type": {"name": "schedule_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "affiliateid": {"name": "affiliateid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "client_name": {"name": "client_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "dosespot_pharmacy_id": {"name": "dosespot_pharmacy_id", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "pharmacy_ncpdp_id": {"name": "pharmacy_ncpdp_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "erx_prescription_visitied_at": {"name": "erx_prescription_visitied_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"telehealth_service_order_service_id_telehealth_services_id_fk": {"name": "telehealth_service_order_service_id_telehealth_services_id_fk", "tableFrom": "telehealth_service_order", "tableTo": "telehealth_services", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "telehealth_service_order_answer_given_by_users_user_id_fk": {"name": "telehealth_service_order_answer_given_by_users_user_id_fk", "tableFrom": "telehealth_service_order", "tableTo": "users", "columnsFrom": ["answer_given_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "telehealth_service_order_provider_id_users_user_id_fk": {"name": "telehealth_service_order_provider_id_users_user_id_fk", "tableFrom": "telehealth_service_order", "tableTo": "users", "columnsFrom": ["provider_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "telehealth_service_order_order_id_orders_id_fk": {"name": "telehealth_service_order_order_id_orders_id_fk", "tableFrom": "telehealth_service_order", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"telehealth_service_order_order_guid_key": {"name": "telehealth_service_order_order_guid_key", "nullsNotDistinct": false, "columns": ["order_guid"]}}}, "public.telehealth_services": {"name": "telehealth_services", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "service_name": {"name": "service_name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "service_type": {"name": "service_type", "type": "enum_telehealth_services_service_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'BUSER'"}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "service_mode": {"name": "service_mode", "type": "enum_telehealth_services_service_mode", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'BOTH_SYNC_ASYNC'"}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": false}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "paypal_plan_id": {"name": "paypal_plan_id", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "subtitle": {"name": "subtitle", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "service_key": {"name": "service_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "session_type": {"name": "session_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "fields_options": {"name": "fields_options", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "on_complete_script": {"name": "on_complete_script", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "on_follow_up_script": {"name": "on_follow_up_script", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "access_labs_test_code": {"name": "access_labs_test_code", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "on_create_script": {"name": "on_create_script", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "on_lab_result_received_script": {"name": "on_lab_result_received_script", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "on_update_schedule_script": {"name": "on_update_schedule_script", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "disclaimer": {"name": "disclaimer", "type": "text", "primaryKey": false, "notNull": false}, "service_details": {"name": "service_details", "type": "text", "primaryKey": false, "notNull": false}, "is_video_call": {"name": "is_video_call", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_audio_call": {"name": "is_audio_call", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "display_questionnaire": {"name": "display_questionnaire", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "display_service_name": {"name": "display_service_name", "type": "text", "primaryKey": false, "notNull": false}, "erx_drug_key": {"name": "erx_drug_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"telehealth_services_service_name_key": {"name": "telehealth_services_service_name_key", "nullsNotDistinct": false, "columns": ["service_name"]}}}, "public.user_files": {"name": "user_files", "schema": "", "columns": {"user_file_id": {"name": "user_file_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "serial", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "serial", "primaryKey": false, "notNull": true}, "doctor_id": {"name": "doctor_id", "type": "serial", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": false}, "order_id": {"name": "order_id", "type": "serial", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_files_user_id_users_user_id_fk": {"name": "user_files_user_id_users_user_id_fk", "tableFrom": "user_files", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_files_created_by_users_user_id_fk": {"name": "user_files_created_by_users_user_id_fk", "tableFrom": "user_files", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_files_doctor_id_users_user_id_fk": {"name": "user_files_doctor_id_users_user_id_fk", "tableFrom": "user_files", "tableTo": "users", "columnsFrom": ["doctor_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {"public.user_gender": {"name": "user_gender", "schema": "public", "values": ["male", "female", "others", "transgender-male", "transgender-female"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["USER", "BUSER", "AUSER", "medical_assistant", "viewer", "support_user", "pharmacist", "PHARMACY", "GROUP_ADMIN", "SUPPORT_ADMIN"]}, "public.user_status": {"name": "user_status", "schema": "public", "values": ["AVAILABLE", "BUSY", "AWAY", "OFFLINE", "ACTIVATION_PENDING", "ONBOARDING_PENDING", "PROFILE_INCOMPLETE"]}, "public.user_sub_role": {"name": "user_sub_role", "schema": "public", "values": ["USER", "DIETICIAN_NUTRITION", "MENTAL_HEALTH", "WEIGHT_LOSS_MANAGEMENT", "DIABETES_PREVENTION"]}, "public.enum_orders_category": {"name": "enum_orders_category", "schema": "public", "values": ["CCM", "RPM", "BHI"]}, "public.enum_orders_status": {"name": "enum_orders_status", "schema": "public", "values": ["STARTED", "INPROGRESS", "ENDED", "NOANSWER", "REJECTED", "QUEUED", "RINGING", "CANCELLED", "COMPLETED", "BUSY", "FAILED"]}, "public.enum_orders_type": {"name": "enum_orders_type", "schema": "public", "values": ["audio", "video", "one-way", "online", "voip"]}, "public.enum_telehealth_service_order_claim_type": {"name": "enum_telehealth_service_order_claim_type", "schema": "public", "values": ["AUTO", "WORK"]}, "public.enum_telehealth_service_order_service_type": {"name": "enum_telehealth_service_order_service_type", "schema": "public", "values": ["SYNC", "ASYNC"]}, "public.enum_telehealth_service_order_status": {"name": "enum_telehealth_service_order_status", "schema": "public", "values": ["pending", "accept", "completed", "errored", "cancelled", "patient_verification_pending", "archive", "cancelled_by_provider", "LabRequested", "LabReceived", "schedule_pending", "lab_approval_pending", "lab_results_approved", "lab_results_denied", "clinical_denial"]}, "public.enum_telehealth_service_order_visit_type": {"name": "enum_telehealth_service_order_visit_type", "schema": "public", "values": ["IN_PERSON", "ONLINE"]}, "public.enum_telehealth_services_service_mode": {"name": "enum_telehealth_services_service_mode", "schema": "public", "values": ["SYNC", "ASYNC", "BOTH_SYNC_ASYNC"]}, "public.enum_telehealth_services_service_type": {"name": "enum_telehealth_services_service_type", "schema": "public", "values": ["BUSER", "AUSER", "medical_assistant", "pharmacist", "DIETICIAN_NUTRITION", "MENTAL_HEALTH", "WEIGHT_LOSS_MANAGEMENT", "DIABETES_PREVENTION"]}}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}