import { relations } from "drizzle-orm/relations";
import { users, auth_provider, orders, consult_notes, telehealth_service_order, educational_videos, user_health_summary, encounters_values, request_objects, requests, encryption_keys, health_summaries_schedule, feed, telehealth_services, promo_codes, drugs_category, drugs, invitations, login_requests, favourite_drugs, immunizations_values, medications_values, pharmacies, mms_patients, mms_prescriptions, onehealth_lab_orders, patient_insurances, schedules, permission_groups, requests_log, consult_update_details_history, chat_room_members, chat_rooms, medicines, medicine_service_pharmacy_mapping, transactions, stripe_user_payment_details, tennant_master, telehealth_service_questions, telehealth_service_provider_mapping, stripe_user_details, social_history_values, user_particlehealth, user_educational_videos, user_details, user_identities, follow_up_reminder, user_file_repo_details, subscription_plans, user_subscription_billing, drug_days, consult_order_files, user_subscription, chat_messages, sms_template, user_vitals_documents, jobs, referrals, chat_files, user_schedules, email_template, prescription_preference, lifefile_configuration, telehealth_service_question_answer_dump, consult_reassign_history, external_requests_log, mms_patient_invitations, support_notes, tenant_auth_provider, diagnoses_values, family_history_values, procedures_values, care_plan_values, results_values, allergies_values, document_values, mms_prescription_refills, vitals_summary_upload_status, vitals_notification_cycle, visit_summary_upload_status, vitals_patient_monitoring, user_forms, forms, user_files, user_diet, telehealth_service_question_answer, faxes, reviews, products, transcriptions, user_vitals, provider_ratings, health_summaries_log, conversations, conversation_messages, prescription_transfer_request, prescription_transfer_medications, refill_request, pharmacy_state_service_mapping, states, telehealth_service_state_mapping, practice_groups, user_practice_groups, schedule_translation, request_translation, users_translation, user_viewers, user_associations } from "./schema";

export const auth_providerRelations = relations(auth_provider, ({one}) => ({
	user: one(users, {
		fields: [auth_provider.user_id],
		references: [users.user_id]
	}),
}));

export const usersRelations = relations(users, ({one, many}) => ({
	auth_providers: many(auth_provider),
	educational_videos: many(educational_videos),
	requests_requestee_id: many(requests, {
		relationName: "requests_requestee_id_users_user_id"
	}),
	requests_requestor_id: many(requests, {
		relationName: "requests_requestor_id_users_user_id"
	}),
	encryption_keys: many(encryption_keys),
	health_summaries_schedules: many(health_summaries_schedule),
	feeds_created_by: many(feed, {
		relationName: "feed_created_by_users_user_id"
	}),
	feeds_user_id: many(feed, {
		relationName: "feed_user_id_users_user_id"
	}),
	invitations: many(invitations),
	login_requests: many(login_requests),
	favourite_drugs: many(favourite_drugs),
	mms_patients: many(mms_patients),
	onehealth_lab_orders: many(onehealth_lab_orders),
	orders_callee_id: many(orders, {
		relationName: "orders_callee_id_users_user_id"
	}),
	orders_caller_id: many(orders, {
		relationName: "orders_caller_id_users_user_id"
	}),
	orders_doctor_id: many(orders, {
		relationName: "orders_doctor_id_users_user_id"
	}),
	permission_groups_associated_user_id: many(permission_groups, {
		relationName: "permission_groups_associated_user_id_users_user_id"
	}),
	permission_groups_patient_id: many(permission_groups, {
		relationName: "permission_groups_patient_id_users_user_id"
	}),
	telehealth_service_orders_answer_given_by: many(telehealth_service_order, {
		relationName: "telehealth_service_order_answer_given_by_users_user_id"
	}),
	telehealth_service_orders_provider_id: many(telehealth_service_order, {
		relationName: "telehealth_service_order_provider_id_users_user_id"
	}),
	requests_logs: many(requests_log),
	consult_update_details_histories: many(consult_update_details_history),
	chat_room_members: many(chat_room_members),
	transactions_payee_user_id: many(transactions, {
		relationName: "transactions_payee_user_id_users_user_id"
	}),
	transactions_payer_user_id: many(transactions, {
		relationName: "transactions_payer_user_id_users_user_id"
	}),
	stripe_user_payment_details: many(stripe_user_payment_details),
	telehealth_service_provider_mappings: many(telehealth_service_provider_mapping),
	stripe_user_details: many(stripe_user_details),
	user_particlehealths_user_id: many(user_particlehealth, {
		relationName: "user_particlehealth_user_id_users_user_id"
	}),
	user_particlehealths_buser_id: many(user_particlehealth, {
		relationName: "user_particlehealth_buser_id_users_user_id"
	}),
	user_educational_videos_referred_by: many(user_educational_videos, {
		relationName: "user_educational_videos_referred_by_users_user_id"
	}),
	user_educational_videos_referred_for: many(user_educational_videos, {
		relationName: "user_educational_videos_referred_for_users_user_id"
	}),
	user_educational_videos_doctor_id: many(user_educational_videos, {
		relationName: "user_educational_videos_doctor_id_users_user_id"
	}),
	user_details: many(user_details),
	user_identities: many(user_identities),
	follow_up_reminders: many(follow_up_reminder),
	user_file_repo_details: many(user_file_repo_details),
	user_health_summaries: many(user_health_summary),
	user_subscription_billings: many(user_subscription_billing),
	user_subscriptions: many(user_subscription),
	chat_messages: many(chat_messages),
	user_vitals_documents_doctor_id: many(user_vitals_documents, {
		relationName: "user_vitals_documents_doctor_id_users_user_id"
	}),
	user_vitals_documents_user_id: many(user_vitals_documents, {
		relationName: "user_vitals_documents_user_id_users_user_id"
	}),
	chat_files: many(chat_files),
	user_schedules_user_id: many(user_schedules, {
		relationName: "user_schedules_user_id_users_user_id"
	}),
	user_schedules_created_by: many(user_schedules, {
		relationName: "user_schedules_created_by_users_user_id"
	}),
	user_schedules_updated_by: many(user_schedules, {
		relationName: "user_schedules_updated_by_users_user_id"
	}),
	user_schedules_deleted_by: many(user_schedules, {
		relationName: "user_schedules_deleted_by_users_user_id"
	}),
	tennant_master: one(tennant_master, {
		fields: [users.tennant_id],
		references: [tennant_master.id]
	}),
	consult_reassign_histories_previous_provider: many(consult_reassign_history, {
		relationName: "consult_reassign_history_previous_provider_users_user_id"
	}),
	consult_reassign_histories_updated_provider: many(consult_reassign_history, {
		relationName: "consult_reassign_history_updated_provider_users_user_id"
	}),
	consult_reassign_histories_reassigned_by: many(consult_reassign_history, {
		relationName: "consult_reassign_history_reassigned_by_users_user_id"
	}),
	external_requests_logs: many(external_requests_log),
	support_notes_user_id: many(support_notes, {
		relationName: "support_notes_user_id_users_user_id"
	}),
	support_notes_support_user_id: many(support_notes, {
		relationName: "support_notes_support_user_id_users_user_id"
	}),
	vitals_summary_upload_statuses_doctor_id: many(vitals_summary_upload_status, {
		relationName: "vitals_summary_upload_status_doctor_id_users_user_id"
	}),
	vitals_summary_upload_statuses_patient_id: many(vitals_summary_upload_status, {
		relationName: "vitals_summary_upload_status_patient_id_users_user_id"
	}),
	vitals_notification_cycles: many(vitals_notification_cycle),
	visit_summary_upload_statuses: many(visit_summary_upload_status),
	vitals_patient_monitorings_buser_id: many(vitals_patient_monitoring, {
		relationName: "vitals_patient_monitoring_buser_id_users_user_id"
	}),
	vitals_patient_monitorings_patient_id: many(vitals_patient_monitoring, {
		relationName: "vitals_patient_monitoring_patient_id_users_user_id"
	}),
	user_forms_assigned_by: many(user_forms, {
		relationName: "user_forms_assigned_by_users_user_id"
	}),
	user_forms_assigned_to: many(user_forms, {
		relationName: "user_forms_assigned_to_users_user_id"
	}),
	user_forms_doctor_id: many(user_forms, {
		relationName: "user_forms_doctor_id_users_user_id"
	}),
	user_files_created_by: many(user_files, {
		relationName: "user_files_created_by_users_user_id"
	}),
	user_files_doctor_id: many(user_files, {
		relationName: "user_files_doctor_id_users_user_id"
	}),
	user_files_user_id: many(user_files, {
		relationName: "user_files_user_id_users_user_id"
	}),
	user_diets: many(user_diet),
	faxes_sent_by: many(faxes, {
		relationName: "faxes_sent_by_users_user_id"
	}),
	faxes_sent_for: many(faxes, {
		relationName: "faxes_sent_for_users_user_id"
	}),
	reviews_given_by: many(reviews, {
		relationName: "reviews_given_by_users_user_id"
	}),
	reviews_given_to: many(reviews, {
		relationName: "reviews_given_to_users_user_id"
	}),
	referrals_doctor_id: many(referrals, {
		relationName: "referrals_doctor_id_users_user_id"
	}),
	referrals_referred_by: many(referrals, {
		relationName: "referrals_referred_by_users_user_id"
	}),
	referrals_referred_for: many(referrals, {
		relationName: "referrals_referred_for_users_user_id"
	}),
	transcriptions: many(transcriptions),
	user_vitals: many(user_vitals),
	provider_ratings_provider_id: many(provider_ratings, {
		relationName: "provider_ratings_provider_id_users_user_id"
	}),
	provider_ratings_user_id: many(provider_ratings, {
		relationName: "provider_ratings_user_id_users_user_id"
	}),
	schedules_scheduled_by: many(schedules, {
		relationName: "schedules_scheduled_by_users_user_id"
	}),
	schedules_scheduled_with: many(schedules, {
		relationName: "schedules_scheduled_with_users_user_id"
	}),
	health_summaries_logs: many(health_summaries_log),
	conversation_messages: many(conversation_messages),
	conversations_user_one: many(conversations, {
		relationName: "conversations_user_one_users_user_id"
	}),
	conversations_user_two: many(conversations, {
		relationName: "conversations_user_two_users_user_id"
	}),
	prescription_transfer_requests: many(prescription_transfer_request),
	user_practice_groups: many(user_practice_groups),
	users_translations: many(users_translation),
	user_viewers_user_id: many(user_viewers, {
		relationName: "user_viewers_user_id_users_user_id"
	}),
	user_viewers_viewer_id: many(user_viewers, {
		relationName: "user_viewers_viewer_id_users_user_id"
	}),
	user_associations_buser_id: many(user_associations, {
		relationName: "user_associations_buser_id_users_user_id"
	}),
	user_associations_user_id: many(user_associations, {
		relationName: "user_associations_user_id_users_user_id"
	}),
}));

export const consult_notesRelations = relations(consult_notes, ({one}) => ({
	order: one(orders, {
		fields: [consult_notes.order_id],
		references: [orders.id]
	}),
	telehealth_service_order: one(telehealth_service_order, {
		fields: [consult_notes.order_guid],
		references: [telehealth_service_order.order_guid]
	}),
}));

export const ordersRelations = relations(orders, ({one, many}) => ({
	consult_notes: many(consult_notes),
	user_callee_id: one(users, {
		fields: [orders.callee_id],
		references: [users.user_id],
		relationName: "orders_callee_id_users_user_id"
	}),
	user_caller_id: one(users, {
		fields: [orders.caller_id],
		references: [users.user_id],
		relationName: "orders_caller_id_users_user_id"
	}),
	user_doctor_id: one(users, {
		fields: [orders.doctor_id],
		references: [users.user_id],
		relationName: "orders_doctor_id_users_user_id"
	}),
	telehealth_service_order: one(telehealth_service_order, {
		fields: [orders.order_guid],
		references: [telehealth_service_order.order_guid],
		relationName: "orders_order_guid_telehealth_service_order_order_guid"
	}),
	schedule: one(schedules, {
		fields: [orders.schedule_id],
		references: [schedules.schedule_id]
	}),
	telehealth_service_orders: many(telehealth_service_order, {
		relationName: "telehealth_service_order_order_id_orders_id"
	}),
	visit_summary_upload_statuses: many(visit_summary_upload_status),
	user_forms: many(user_forms),
	user_files: many(user_files),
	user_diets: many(user_diet),
	reviews: many(reviews),
	referrals: many(referrals),
	transcriptions: many(transcriptions),
	user_vitals: many(user_vitals),
	provider_ratings: many(provider_ratings),
}));

export const telehealth_service_orderRelations = relations(telehealth_service_order, ({one, many}) => ({
	consult_notes: many(consult_notes),
	requests: many(requests),
	orders: many(orders, {
		relationName: "orders_order_guid_telehealth_service_order_order_guid"
	}),
	user_answer_given_by: one(users, {
		fields: [telehealth_service_order.answer_given_by],
		references: [users.user_id],
		relationName: "telehealth_service_order_answer_given_by_users_user_id"
	}),
	order: one(orders, {
		fields: [telehealth_service_order.order_id],
		references: [orders.id],
		relationName: "telehealth_service_order_order_id_orders_id"
	}),
	user_provider_id: one(users, {
		fields: [telehealth_service_order.provider_id],
		references: [users.user_id],
		relationName: "telehealth_service_order_provider_id_users_user_id"
	}),
	telehealth_service: one(telehealth_services, {
		fields: [telehealth_service_order.service_id],
		references: [telehealth_services.id]
	}),
	consult_update_details_histories: many(consult_update_details_history),
	follow_up_reminders: many(follow_up_reminder),
	consult_order_files: many(consult_order_files),
	jobs: many(jobs),
	telehealth_service_question_answer_dumps: many(telehealth_service_question_answer_dump),
	consult_reassign_histories: many(consult_reassign_history),
	support_notes: many(support_notes),
	telehealth_service_question_answers: many(telehealth_service_question_answer),
	schedules: many(schedules),
	refill_requests: many(refill_request),
}));

export const educational_videosRelations = relations(educational_videos, ({one, many}) => ({
	user: one(users, {
		fields: [educational_videos.created_by],
		references: [users.user_id]
	}),
	user_educational_videos: many(user_educational_videos),
}));

export const encounters_valuesRelations = relations(encounters_values, ({one}) => ({
	user_health_summary: one(user_health_summary, {
		fields: [encounters_values.summary_id],
		references: [user_health_summary.summary_id]
	}),
}));

export const user_health_summaryRelations = relations(user_health_summary, ({one, many}) => ({
	encounters_values: many(encounters_values),
	immunizations_values: many(immunizations_values),
	medications_values: many(medications_values),
	social_history_values: many(social_history_values),
	user: one(users, {
		fields: [user_health_summary.user_id],
		references: [users.user_id]
	}),
	diagnoses_values: many(diagnoses_values),
	family_history_values: many(family_history_values),
	procedures_values: many(procedures_values),
	care_plan_values: many(care_plan_values),
	results_values: many(results_values),
	allergies_values: many(allergies_values),
	document_values: many(document_values),
}));

export const requestsRelations = relations(requests, ({one, many}) => ({
	request_object: one(request_objects, {
		fields: [requests.object_id],
		references: [request_objects.object_id]
	}),
	telehealth_service_order: one(telehealth_service_order, {
		fields: [requests.order_guid],
		references: [telehealth_service_order.order_guid]
	}),
	user_requestee_id: one(users, {
		fields: [requests.requestee_id],
		references: [users.user_id],
		relationName: "requests_requestee_id_users_user_id"
	}),
	user_requestor_id: one(users, {
		fields: [requests.requestor_id],
		references: [users.user_id],
		relationName: "requests_requestor_id_users_user_id"
	}),
	request_translations: many(request_translation),
}));

export const request_objectsRelations = relations(request_objects, ({many}) => ({
	requests: many(requests),
}));

export const encryption_keysRelations = relations(encryption_keys, ({one}) => ({
	user: one(users, {
		fields: [encryption_keys.user_id],
		references: [users.user_id]
	}),
}));

export const health_summaries_scheduleRelations = relations(health_summaries_schedule, ({one}) => ({
	user: one(users, {
		fields: [health_summaries_schedule.user_id],
		references: [users.user_id]
	}),
}));

export const feedRelations = relations(feed, ({one}) => ({
	user_created_by: one(users, {
		fields: [feed.created_by],
		references: [users.user_id],
		relationName: "feed_created_by_users_user_id"
	}),
	user_user_id: one(users, {
		fields: [feed.user_id],
		references: [users.user_id],
		relationName: "feed_user_id_users_user_id"
	}),
}));

export const promo_codesRelations = relations(promo_codes, ({one}) => ({
	telehealth_service: one(telehealth_services, {
		fields: [promo_codes.service_id],
		references: [telehealth_services.id]
	}),
}));

export const telehealth_servicesRelations = relations(telehealth_services, ({many}) => ({
	promo_codes: many(promo_codes),
	telehealth_service_orders: many(telehealth_service_order),
	medicine_service_pharmacy_mappings: many(medicine_service_pharmacy_mapping),
	telehealth_service_questions: many(telehealth_service_questions),
	telehealth_service_provider_mappings: many(telehealth_service_provider_mapping),
	sms_templates: many(sms_template),
	email_templates: many(email_template),
	pharmacy_state_service_mappings: many(pharmacy_state_service_mapping),
	telehealth_service_state_mappings: many(telehealth_service_state_mapping),
}));

export const drugsRelations = relations(drugs, ({one, many}) => ({
	drugs_category: one(drugs_category, {
		fields: [drugs.category_id],
		references: [drugs_category.category_id]
	}),
	drug_days: many(drug_days),
}));

export const drugs_categoryRelations = relations(drugs_category, ({many}) => ({
	drugs: many(drugs),
}));

export const invitationsRelations = relations(invitations, ({one}) => ({
	user: one(users, {
		fields: [invitations.invitor_id],
		references: [users.user_id]
	}),
}));

export const login_requestsRelations = relations(login_requests, ({one}) => ({
	user: one(users, {
		fields: [login_requests.user_id],
		references: [users.user_id]
	}),
}));

export const favourite_drugsRelations = relations(favourite_drugs, ({one}) => ({
	user: one(users, {
		fields: [favourite_drugs.user_id],
		references: [users.user_id]
	}),
}));

export const immunizations_valuesRelations = relations(immunizations_values, ({one}) => ({
	user_health_summary: one(user_health_summary, {
		fields: [immunizations_values.summary_id],
		references: [user_health_summary.summary_id]
	}),
}));

export const medications_valuesRelations = relations(medications_values, ({one}) => ({
	user_health_summary: one(user_health_summary, {
		fields: [medications_values.summary_id],
		references: [user_health_summary.summary_id]
	}),
}));

export const mms_patientsRelations = relations(mms_patients, ({one, many}) => ({
	pharmacy: one(pharmacies, {
		fields: [mms_patients.pharmacy_id],
		references: [pharmacies.pharmacy_id]
	}),
	user: one(users, {
		fields: [mms_patients.user_id],
		references: [users.user_id]
	}),
	mms_prescriptions: many(mms_prescriptions),
	patient_insurances: many(patient_insurances),
	mms_patient_invitations: many(mms_patient_invitations),
}));

export const pharmaciesRelations = relations(pharmacies, ({many}) => ({
	mms_patients: many(mms_patients),
	medicine_service_pharmacy_mappings: many(medicine_service_pharmacy_mapping),
	prescription_preferences: many(prescription_preference),
	lifefile_configurations: many(lifefile_configuration),
	pharmacy_state_service_mappings: many(pharmacy_state_service_mapping),
}));

export const mms_prescriptionsRelations = relations(mms_prescriptions, ({one, many}) => ({
	mms_patient: one(mms_patients, {
		fields: [mms_prescriptions.patient_id],
		references: [mms_patients.patient_id]
	}),
	mms_prescription_refills: many(mms_prescription_refills),
}));

export const onehealth_lab_ordersRelations = relations(onehealth_lab_orders, ({one}) => ({
	user: one(users, {
		fields: [onehealth_lab_orders.user_id],
		references: [users.user_id]
	}),
}));

export const patient_insurancesRelations = relations(patient_insurances, ({one}) => ({
	mms_patient: one(mms_patients, {
		fields: [patient_insurances.patient_id],
		references: [mms_patients.patient_id]
	}),
}));

export const schedulesRelations = relations(schedules, ({one, many}) => ({
	orders: many(orders),
	telehealth_service_order: one(telehealth_service_order, {
		fields: [schedules.order_guid],
		references: [telehealth_service_order.order_guid]
	}),
	user_scheduled_by: one(users, {
		fields: [schedules.scheduled_by],
		references: [users.user_id],
		relationName: "schedules_scheduled_by_users_user_id"
	}),
	user_scheduled_with: one(users, {
		fields: [schedules.scheduled_with],
		references: [users.user_id],
		relationName: "schedules_scheduled_with_users_user_id"
	}),
	schedule_translations: many(schedule_translation),
}));

export const permission_groupsRelations = relations(permission_groups, ({one}) => ({
	user_associated_user_id: one(users, {
		fields: [permission_groups.associated_user_id],
		references: [users.user_id],
		relationName: "permission_groups_associated_user_id_users_user_id"
	}),
	user_patient_id: one(users, {
		fields: [permission_groups.patient_id],
		references: [users.user_id],
		relationName: "permission_groups_patient_id_users_user_id"
	}),
}));

export const requests_logRelations = relations(requests_log, ({one}) => ({
	user: one(users, {
		fields: [requests_log.user_id],
		references: [users.user_id]
	}),
}));

export const consult_update_details_historyRelations = relations(consult_update_details_history, ({one}) => ({
	telehealth_service_order: one(telehealth_service_order, {
		fields: [consult_update_details_history.service_order_id],
		references: [telehealth_service_order.id]
	}),
	user: one(users, {
		fields: [consult_update_details_history.user_id],
		references: [users.user_id]
	}),
}));

export const chat_room_membersRelations = relations(chat_room_members, ({one}) => ({
	user: one(users, {
		fields: [chat_room_members.user_id],
		references: [users.user_id]
	}),
	chat_room: one(chat_rooms, {
		fields: [chat_room_members.room_id],
		references: [chat_rooms.id]
	}),
}));

export const chat_roomsRelations = relations(chat_rooms, ({many}) => ({
	chat_room_members: many(chat_room_members),
	chat_messages: many(chat_messages),
	chat_files: many(chat_files),
}));

export const medicine_service_pharmacy_mappingRelations = relations(medicine_service_pharmacy_mapping, ({one}) => ({
	medicine: one(medicines, {
		fields: [medicine_service_pharmacy_mapping.medicine_id],
		references: [medicines.medicine_id]
	}),
	pharmacy: one(pharmacies, {
		fields: [medicine_service_pharmacy_mapping.pharmacy_id],
		references: [pharmacies.pharmacy_id]
	}),
	telehealth_service: one(telehealth_services, {
		fields: [medicine_service_pharmacy_mapping.service_id],
		references: [telehealth_services.id]
	}),
}));

export const medicinesRelations = relations(medicines, ({many}) => ({
	medicine_service_pharmacy_mappings: many(medicine_service_pharmacy_mapping),
}));

export const transactionsRelations = relations(transactions, ({one}) => ({
	user_payee_user_id: one(users, {
		fields: [transactions.payee_user_id],
		references: [users.user_id],
		relationName: "transactions_payee_user_id_users_user_id"
	}),
	user_payer_user_id: one(users, {
		fields: [transactions.payer_user_id],
		references: [users.user_id],
		relationName: "transactions_payer_user_id_users_user_id"
	}),
}));

export const stripe_user_payment_detailsRelations = relations(stripe_user_payment_details, ({one}) => ({
	user: one(users, {
		fields: [stripe_user_payment_details.user_id],
		references: [users.user_id]
	}),
}));

export const tennant_masterRelations = relations(tennant_master, ({one, many}) => ({
	tennant_master: one(tennant_master, {
		fields: [tennant_master.parent_id],
		references: [tennant_master.id],
		relationName: "tennant_master_parent_id_tennant_master_id"
	}),
	tennant_masters: many(tennant_master, {
		relationName: "tennant_master_parent_id_tennant_master_id"
	}),
	follow_up_reminders: many(follow_up_reminder),
	consult_order_files: many(consult_order_files),
	prescription_preferences: many(prescription_preference),
	lifefile_configurations: many(lifefile_configuration),
	users: many(users),
	tenant_auth_providers: many(tenant_auth_provider),
}));

export const telehealth_service_questionsRelations = relations(telehealth_service_questions, ({one, many}) => ({
	telehealth_service: one(telehealth_services, {
		fields: [telehealth_service_questions.service_id],
		references: [telehealth_services.id]
	}),
	telehealth_service_question_answers: many(telehealth_service_question_answer),
}));

export const telehealth_service_provider_mappingRelations = relations(telehealth_service_provider_mapping, ({one}) => ({
	user: one(users, {
		fields: [telehealth_service_provider_mapping.provider_id],
		references: [users.user_id]
	}),
	telehealth_service: one(telehealth_services, {
		fields: [telehealth_service_provider_mapping.service_id],
		references: [telehealth_services.id]
	}),
}));

export const stripe_user_detailsRelations = relations(stripe_user_details, ({one}) => ({
	user: one(users, {
		fields: [stripe_user_details.user_id],
		references: [users.user_id]
	}),
}));

export const social_history_valuesRelations = relations(social_history_values, ({one}) => ({
	user_health_summary: one(user_health_summary, {
		fields: [social_history_values.summary_id],
		references: [user_health_summary.summary_id]
	}),
}));

export const user_particlehealthRelations = relations(user_particlehealth, ({one}) => ({
	user_user_id: one(users, {
		fields: [user_particlehealth.user_id],
		references: [users.user_id],
		relationName: "user_particlehealth_user_id_users_user_id"
	}),
	user_buser_id: one(users, {
		fields: [user_particlehealth.buser_id],
		references: [users.user_id],
		relationName: "user_particlehealth_buser_id_users_user_id"
	}),
}));

export const user_educational_videosRelations = relations(user_educational_videos, ({one}) => ({
	user_referred_by: one(users, {
		fields: [user_educational_videos.referred_by],
		references: [users.user_id],
		relationName: "user_educational_videos_referred_by_users_user_id"
	}),
	user_referred_for: one(users, {
		fields: [user_educational_videos.referred_for],
		references: [users.user_id],
		relationName: "user_educational_videos_referred_for_users_user_id"
	}),
	user_doctor_id: one(users, {
		fields: [user_educational_videos.doctor_id],
		references: [users.user_id],
		relationName: "user_educational_videos_doctor_id_users_user_id"
	}),
	educational_video: one(educational_videos, {
		fields: [user_educational_videos.video_id],
		references: [educational_videos.video_id]
	}),
}));

export const user_detailsRelations = relations(user_details, ({one}) => ({
	user: one(users, {
		fields: [user_details.user_id],
		references: [users.user_id]
	}),
}));

export const user_identitiesRelations = relations(user_identities, ({one}) => ({
	user: one(users, {
		fields: [user_identities.user_id],
		references: [users.user_id]
	}),
}));

export const follow_up_reminderRelations = relations(follow_up_reminder, ({one}) => ({
	telehealth_service_order: one(telehealth_service_order, {
		fields: [follow_up_reminder.order_id],
		references: [telehealth_service_order.id]
	}),
	user: one(users, {
		fields: [follow_up_reminder.user_id],
		references: [users.user_id]
	}),
	tennant_master: one(tennant_master, {
		fields: [follow_up_reminder.tennant_id],
		references: [tennant_master.id]
	}),
}));

export const user_file_repo_detailsRelations = relations(user_file_repo_details, ({one}) => ({
	user: one(users, {
		fields: [user_file_repo_details.user_id],
		references: [users.user_id]
	}),
}));

export const user_subscription_billingRelations = relations(user_subscription_billing, ({one}) => ({
	subscription_plan: one(subscription_plans, {
		fields: [user_subscription_billing.plan_id],
		references: [subscription_plans.plan_id]
	}),
	user: one(users, {
		fields: [user_subscription_billing.user_id],
		references: [users.user_id]
	}),
}));

export const subscription_plansRelations = relations(subscription_plans, ({many}) => ({
	user_subscription_billings: many(user_subscription_billing),
	user_subscriptions: many(user_subscription),
}));

export const drug_daysRelations = relations(drug_days, ({one}) => ({
	drug: one(drugs, {
		fields: [drug_days.drug_id],
		references: [drugs.drug_id]
	}),
}));

export const consult_order_filesRelations = relations(consult_order_files, ({one}) => ({
	telehealth_service_order: one(telehealth_service_order, {
		fields: [consult_order_files.order_id],
		references: [telehealth_service_order.id]
	}),
	tennant_master: one(tennant_master, {
		fields: [consult_order_files.tennant_id],
		references: [tennant_master.id]
	}),
}));

export const user_subscriptionRelations = relations(user_subscription, ({one}) => ({
	subscription_plan: one(subscription_plans, {
		fields: [user_subscription.plan_id],
		references: [subscription_plans.plan_id]
	}),
	user: one(users, {
		fields: [user_subscription.user_id],
		references: [users.user_id]
	}),
}));

export const chat_messagesRelations = relations(chat_messages, ({one}) => ({
	user: one(users, {
		fields: [chat_messages.sender_id],
		references: [users.user_id]
	}),
	chat_room: one(chat_rooms, {
		fields: [chat_messages.room_id],
		references: [chat_rooms.id]
	}),
}));

export const sms_templateRelations = relations(sms_template, ({one}) => ({
	telehealth_service: one(telehealth_services, {
		fields: [sms_template.service_id],
		references: [telehealth_services.id]
	}),
}));

export const user_vitals_documentsRelations = relations(user_vitals_documents, ({one}) => ({
	user_doctor_id: one(users, {
		fields: [user_vitals_documents.doctor_id],
		references: [users.user_id],
		relationName: "user_vitals_documents_doctor_id_users_user_id"
	}),
	user_user_id: one(users, {
		fields: [user_vitals_documents.user_id],
		references: [users.user_id],
		relationName: "user_vitals_documents_user_id_users_user_id"
	}),
}));

export const jobsRelations = relations(jobs, ({one}) => ({
	telehealth_service_order: one(telehealth_service_order, {
		fields: [jobs.order_id],
		references: [telehealth_service_order.id]
	}),
	referral: one(referrals, {
		fields: [jobs.referral_id],
		references: [referrals.referral_id]
	}),
}));

export const referralsRelations = relations(referrals, ({one, many}) => ({
	jobs: many(jobs),
	faxes: many(faxes),
	user_doctor_id: one(users, {
		fields: [referrals.doctor_id],
		references: [users.user_id],
		relationName: "referrals_doctor_id_users_user_id"
	}),
	order: one(orders, {
		fields: [referrals.order_id],
		references: [orders.id]
	}),
	user_referred_by: one(users, {
		fields: [referrals.referred_by],
		references: [users.user_id],
		relationName: "referrals_referred_by_users_user_id"
	}),
	user_referred_for: one(users, {
		fields: [referrals.referred_for],
		references: [users.user_id],
		relationName: "referrals_referred_for_users_user_id"
	}),
	product: one(products, {
		fields: [referrals.product_id],
		references: [products.product_id]
	}),
}));

export const chat_filesRelations = relations(chat_files, ({one}) => ({
	user: one(users, {
		fields: [chat_files.user_id],
		references: [users.user_id]
	}),
	chat_room: one(chat_rooms, {
		fields: [chat_files.room_id],
		references: [chat_rooms.id]
	}),
}));

export const user_schedulesRelations = relations(user_schedules, ({one}) => ({
	user_user_id: one(users, {
		fields: [user_schedules.user_id],
		references: [users.user_id],
		relationName: "user_schedules_user_id_users_user_id"
	}),
	user_created_by: one(users, {
		fields: [user_schedules.created_by],
		references: [users.user_id],
		relationName: "user_schedules_created_by_users_user_id"
	}),
	user_updated_by: one(users, {
		fields: [user_schedules.updated_by],
		references: [users.user_id],
		relationName: "user_schedules_updated_by_users_user_id"
	}),
	user_deleted_by: one(users, {
		fields: [user_schedules.deleted_by],
		references: [users.user_id],
		relationName: "user_schedules_deleted_by_users_user_id"
	}),
}));

export const email_templateRelations = relations(email_template, ({one}) => ({
	telehealth_service: one(telehealth_services, {
		fields: [email_template.service_id],
		references: [telehealth_services.id]
	}),
}));

export const prescription_preferenceRelations = relations(prescription_preference, ({one}) => ({
	pharmacy: one(pharmacies, {
		fields: [prescription_preference.pharmacy_id],
		references: [pharmacies.pharmacy_id]
	}),
	tennant_master: one(tennant_master, {
		fields: [prescription_preference.tennant_id],
		references: [tennant_master.id]
	}),
}));

export const lifefile_configurationRelations = relations(lifefile_configuration, ({one}) => ({
	tennant_master: one(tennant_master, {
		fields: [lifefile_configuration.tennant_id],
		references: [tennant_master.id]
	}),
	pharmacy: one(pharmacies, {
		fields: [lifefile_configuration.pharmacy_id],
		references: [pharmacies.pharmacy_id]
	}),
}));

export const telehealth_service_question_answer_dumpRelations = relations(telehealth_service_question_answer_dump, ({one}) => ({
	telehealth_service_order: one(telehealth_service_order, {
		fields: [telehealth_service_question_answer_dump.service_order_id],
		references: [telehealth_service_order.id]
	}),
}));

export const consult_reassign_historyRelations = relations(consult_reassign_history, ({one}) => ({
	telehealth_service_order: one(telehealth_service_order, {
		fields: [consult_reassign_history.service_order_id],
		references: [telehealth_service_order.id]
	}),
	user_previous_provider: one(users, {
		fields: [consult_reassign_history.previous_provider],
		references: [users.user_id],
		relationName: "consult_reassign_history_previous_provider_users_user_id"
	}),
	user_updated_provider: one(users, {
		fields: [consult_reassign_history.updated_provider],
		references: [users.user_id],
		relationName: "consult_reassign_history_updated_provider_users_user_id"
	}),
	user_reassigned_by: one(users, {
		fields: [consult_reassign_history.reassigned_by],
		references: [users.user_id],
		relationName: "consult_reassign_history_reassigned_by_users_user_id"
	}),
}));

export const external_requests_logRelations = relations(external_requests_log, ({one}) => ({
	user: one(users, {
		fields: [external_requests_log.user_id],
		references: [users.user_id]
	}),
}));

export const mms_patient_invitationsRelations = relations(mms_patient_invitations, ({one}) => ({
	mms_patient: one(mms_patients, {
		fields: [mms_patient_invitations.mms_patient_id],
		references: [mms_patients.patient_id]
	}),
}));

export const support_notesRelations = relations(support_notes, ({one}) => ({
	user_user_id: one(users, {
		fields: [support_notes.user_id],
		references: [users.user_id],
		relationName: "support_notes_user_id_users_user_id"
	}),
	user_support_user_id: one(users, {
		fields: [support_notes.support_user_id],
		references: [users.user_id],
		relationName: "support_notes_support_user_id_users_user_id"
	}),
	telehealth_service_order: one(telehealth_service_order, {
		fields: [support_notes.order_guid],
		references: [telehealth_service_order.order_guid]
	}),
}));

export const tenant_auth_providerRelations = relations(tenant_auth_provider, ({one}) => ({
	tennant_master: one(tennant_master, {
		fields: [tenant_auth_provider.tenant_id],
		references: [tennant_master.id]
	}),
}));

export const diagnoses_valuesRelations = relations(diagnoses_values, ({one}) => ({
	user_health_summary: one(user_health_summary, {
		fields: [diagnoses_values.summary_id],
		references: [user_health_summary.summary_id]
	}),
}));

export const family_history_valuesRelations = relations(family_history_values, ({one}) => ({
	user_health_summary: one(user_health_summary, {
		fields: [family_history_values.summary_id],
		references: [user_health_summary.summary_id]
	}),
}));

export const procedures_valuesRelations = relations(procedures_values, ({one}) => ({
	user_health_summary: one(user_health_summary, {
		fields: [procedures_values.summary_id],
		references: [user_health_summary.summary_id]
	}),
}));

export const care_plan_valuesRelations = relations(care_plan_values, ({one}) => ({
	user_health_summary: one(user_health_summary, {
		fields: [care_plan_values.summary_id],
		references: [user_health_summary.summary_id]
	}),
}));

export const results_valuesRelations = relations(results_values, ({one}) => ({
	user_health_summary: one(user_health_summary, {
		fields: [results_values.summary_id],
		references: [user_health_summary.summary_id]
	}),
}));

export const allergies_valuesRelations = relations(allergies_values, ({one}) => ({
	user_health_summary: one(user_health_summary, {
		fields: [allergies_values.summary_id],
		references: [user_health_summary.summary_id]
	}),
}));

export const document_valuesRelations = relations(document_values, ({one}) => ({
	user_health_summary: one(user_health_summary, {
		fields: [document_values.summary_id],
		references: [user_health_summary.summary_id]
	}),
}));

export const mms_prescription_refillsRelations = relations(mms_prescription_refills, ({one}) => ({
	mms_prescription: one(mms_prescriptions, {
		fields: [mms_prescription_refills.prescription_id],
		references: [mms_prescriptions.prescription_id]
	}),
}));

export const vitals_summary_upload_statusRelations = relations(vitals_summary_upload_status, ({one}) => ({
	user_doctor_id: one(users, {
		fields: [vitals_summary_upload_status.doctor_id],
		references: [users.user_id],
		relationName: "vitals_summary_upload_status_doctor_id_users_user_id"
	}),
	user_patient_id: one(users, {
		fields: [vitals_summary_upload_status.patient_id],
		references: [users.user_id],
		relationName: "vitals_summary_upload_status_patient_id_users_user_id"
	}),
}));

export const vitals_notification_cycleRelations = relations(vitals_notification_cycle, ({one}) => ({
	user: one(users, {
		fields: [vitals_notification_cycle.user_id],
		references: [users.user_id]
	}),
}));

export const visit_summary_upload_statusRelations = relations(visit_summary_upload_status, ({one}) => ({
	order: one(orders, {
		fields: [visit_summary_upload_status.order_id],
		references: [orders.order_id]
	}),
	user: one(users, {
		fields: [visit_summary_upload_status.user_id],
		references: [users.user_id]
	}),
}));

export const vitals_patient_monitoringRelations = relations(vitals_patient_monitoring, ({one}) => ({
	user_buser_id: one(users, {
		fields: [vitals_patient_monitoring.buser_id],
		references: [users.user_id],
		relationName: "vitals_patient_monitoring_buser_id_users_user_id"
	}),
	user_patient_id: one(users, {
		fields: [vitals_patient_monitoring.patient_id],
		references: [users.user_id],
		relationName: "vitals_patient_monitoring_patient_id_users_user_id"
	}),
}));

export const user_formsRelations = relations(user_forms, ({one}) => ({
	user_assigned_by: one(users, {
		fields: [user_forms.assigned_by],
		references: [users.user_id],
		relationName: "user_forms_assigned_by_users_user_id"
	}),
	user_assigned_to: one(users, {
		fields: [user_forms.assigned_to],
		references: [users.user_id],
		relationName: "user_forms_assigned_to_users_user_id"
	}),
	user_doctor_id: one(users, {
		fields: [user_forms.doctor_id],
		references: [users.user_id],
		relationName: "user_forms_doctor_id_users_user_id"
	}),
	form: one(forms, {
		fields: [user_forms.form_id],
		references: [forms.form_id]
	}),
	order: one(orders, {
		fields: [user_forms.order_id],
		references: [orders.id]
	}),
}));

export const formsRelations = relations(forms, ({many}) => ({
	user_forms: many(user_forms),
}));

export const user_filesRelations = relations(user_files, ({one}) => ({
	user_created_by: one(users, {
		fields: [user_files.created_by],
		references: [users.user_id],
		relationName: "user_files_created_by_users_user_id"
	}),
	user_doctor_id: one(users, {
		fields: [user_files.doctor_id],
		references: [users.user_id],
		relationName: "user_files_doctor_id_users_user_id"
	}),
	order: one(orders, {
		fields: [user_files.order_id],
		references: [orders.id]
	}),
	user_user_id: one(users, {
		fields: [user_files.user_id],
		references: [users.user_id],
		relationName: "user_files_user_id_users_user_id"
	}),
}));

export const user_dietRelations = relations(user_diet, ({one}) => ({
	order: one(orders, {
		fields: [user_diet.order_id],
		references: [orders.id]
	}),
	user: one(users, {
		fields: [user_diet.user_id],
		references: [users.user_id]
	}),
}));

export const telehealth_service_question_answerRelations = relations(telehealth_service_question_answer, ({one}) => ({
	telehealth_service_question: one(telehealth_service_questions, {
		fields: [telehealth_service_question_answer.question_id],
		references: [telehealth_service_questions.id]
	}),
	telehealth_service_order: one(telehealth_service_order, {
		fields: [telehealth_service_question_answer.service_order_id],
		references: [telehealth_service_order.id]
	}),
}));

export const faxesRelations = relations(faxes, ({one}) => ({
	referral: one(referrals, {
		fields: [faxes.referral_id],
		references: [referrals.referral_id]
	}),
	user_sent_by: one(users, {
		fields: [faxes.sent_by],
		references: [users.user_id],
		relationName: "faxes_sent_by_users_user_id"
	}),
	user_sent_for: one(users, {
		fields: [faxes.sent_for],
		references: [users.user_id],
		relationName: "faxes_sent_for_users_user_id"
	}),
}));

export const reviewsRelations = relations(reviews, ({one}) => ({
	user_given_by: one(users, {
		fields: [reviews.given_by],
		references: [users.user_id],
		relationName: "reviews_given_by_users_user_id"
	}),
	user_given_to: one(users, {
		fields: [reviews.given_to],
		references: [users.user_id],
		relationName: "reviews_given_to_users_user_id"
	}),
	order: one(orders, {
		fields: [reviews.order_id],
		references: [orders.order_id]
	}),
}));

export const productsRelations = relations(products, ({many}) => ({
	referrals: many(referrals),
}));

export const transcriptionsRelations = relations(transcriptions, ({one}) => ({
	order: one(orders, {
		fields: [transcriptions.order_id],
		references: [orders.order_id]
	}),
	user: one(users, {
		fields: [transcriptions.tuser_id],
		references: [users.user_id]
	}),
}));

export const user_vitalsRelations = relations(user_vitals, ({one}) => ({
	order: one(orders, {
		fields: [user_vitals.order_id],
		references: [orders.id]
	}),
	user: one(users, {
		fields: [user_vitals.user_id],
		references: [users.user_id]
	}),
}));

export const provider_ratingsRelations = relations(provider_ratings, ({one}) => ({
	order: one(orders, {
		fields: [provider_ratings.order_id],
		references: [orders.id]
	}),
	user_provider_id: one(users, {
		fields: [provider_ratings.provider_id],
		references: [users.user_id],
		relationName: "provider_ratings_provider_id_users_user_id"
	}),
	user_user_id: one(users, {
		fields: [provider_ratings.user_id],
		references: [users.user_id],
		relationName: "provider_ratings_user_id_users_user_id"
	}),
}));

export const health_summaries_logRelations = relations(health_summaries_log, ({one}) => ({
	user: one(users, {
		fields: [health_summaries_log.user_id],
		references: [users.user_id]
	}),
}));

export const conversation_messagesRelations = relations(conversation_messages, ({one}) => ({
	conversation: one(conversations, {
		fields: [conversation_messages.c_id],
		references: [conversations.c_id]
	}),
	user: one(users, {
		fields: [conversation_messages.user_id],
		references: [users.user_id]
	}),
}));

export const conversationsRelations = relations(conversations, ({one, many}) => ({
	conversation_messages: many(conversation_messages),
	user_user_one: one(users, {
		fields: [conversations.user_one],
		references: [users.user_id],
		relationName: "conversations_user_one_users_user_id"
	}),
	user_user_two: one(users, {
		fields: [conversations.user_two],
		references: [users.user_id],
		relationName: "conversations_user_two_users_user_id"
	}),
}));

export const prescription_transfer_medicationsRelations = relations(prescription_transfer_medications, ({one}) => ({
	prescription_transfer_request: one(prescription_transfer_request, {
		fields: [prescription_transfer_medications.request_id],
		references: [prescription_transfer_request.request_id]
	}),
}));

export const prescription_transfer_requestRelations = relations(prescription_transfer_request, ({one, many}) => ({
	prescription_transfer_medications: many(prescription_transfer_medications),
	user: one(users, {
		fields: [prescription_transfer_request.user_id],
		references: [users.user_id]
	}),
}));

export const refill_requestRelations = relations(refill_request, ({one}) => ({
	telehealth_service_order: one(telehealth_service_order, {
		fields: [refill_request.service_order_id],
		references: [telehealth_service_order.id]
	}),
}));

export const pharmacy_state_service_mappingRelations = relations(pharmacy_state_service_mapping, ({one}) => ({
	telehealth_service: one(telehealth_services, {
		fields: [pharmacy_state_service_mapping.service_id],
		references: [telehealth_services.id]
	}),
	state: one(states, {
		fields: [pharmacy_state_service_mapping.state_id],
		references: [states.state_id]
	}),
	pharmacy: one(pharmacies, {
		fields: [pharmacy_state_service_mapping.pharmacy_id],
		references: [pharmacies.pharmacy_id]
	}),
}));

export const statesRelations = relations(states, ({many}) => ({
	pharmacy_state_service_mappings: many(pharmacy_state_service_mapping),
	telehealth_service_state_mappings: many(telehealth_service_state_mapping),
}));

export const telehealth_service_state_mappingRelations = relations(telehealth_service_state_mapping, ({one}) => ({
	state: one(states, {
		fields: [telehealth_service_state_mapping.state_id],
		references: [states.state_id]
	}),
	telehealth_service: one(telehealth_services, {
		fields: [telehealth_service_state_mapping.service_id],
		references: [telehealth_services.id]
	}),
}));

export const user_practice_groupsRelations = relations(user_practice_groups, ({one}) => ({
	practice_group: one(practice_groups, {
		fields: [user_practice_groups.practice_group_id],
		references: [practice_groups.practice_group_id]
	}),
	user: one(users, {
		fields: [user_practice_groups.user_id],
		references: [users.user_id]
	}),
}));

export const practice_groupsRelations = relations(practice_groups, ({many}) => ({
	user_practice_groups: many(user_practice_groups),
}));

export const schedule_translationRelations = relations(schedule_translation, ({one}) => ({
	schedule: one(schedules, {
		fields: [schedule_translation.schedule_id],
		references: [schedules.schedule_id]
	}),
}));

export const request_translationRelations = relations(request_translation, ({one}) => ({
	request: one(requests, {
		fields: [request_translation.request_id],
		references: [requests.request_id]
	}),
}));

export const users_translationRelations = relations(users_translation, ({one}) => ({
	user: one(users, {
		fields: [users_translation.user_id],
		references: [users.user_id]
	}),
}));

export const user_viewersRelations = relations(user_viewers, ({one}) => ({
	user_user_id: one(users, {
		fields: [user_viewers.user_id],
		references: [users.user_id],
		relationName: "user_viewers_user_id_users_user_id"
	}),
	user_viewer_id: one(users, {
		fields: [user_viewers.viewer_id],
		references: [users.user_id],
		relationName: "user_viewers_viewer_id_users_user_id"
	}),
}));

export const user_associationsRelations = relations(user_associations, ({one}) => ({
	user_buser_id: one(users, {
		fields: [user_associations.buser_id],
		references: [users.user_id],
		relationName: "user_associations_buser_id_users_user_id"
	}),
	user_user_id: one(users, {
		fields: [user_associations.user_id],
		references: [users.user_id],
		relationName: "user_associations_user_id_users_user_id"
	}),
}));