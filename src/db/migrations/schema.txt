import { pgTable, pgEnum, varchar, foreignKey, serial, integer, text, timestamp, boolean, numeric, unique, type AnyPgColumn, uuid, doublePrecision, index, primaryKey } from "drizzle-orm/pg-core"
  import { sql } from "drizzle-orm"

export const enum_consult_order_files_type = pgEnum("enum_consult_order_files_type", ['audio', 'video', 'file', 'image'])
export const enum_follow_up_reminder_status = pgEnum("enum_follow_up_reminder_status", ['pending', 'started', 'failed', 'sent', 'cancel'])
export const enum_jobs_status = pgEnum("enum_jobs_status", ['pending', 'started', 'failed', 'sending_fax', 'completed'])
export const enum_lifefile_configuration_shipping_services = pgEnum("enum_lifefile_configuration_shipping_services", ['7780', '9'])
export const enum_onehealth_lab_orders_payment_status = pgEnum("enum_onehealth_lab_orders_payment_status", ['PENDING', 'FAILED', 'PAID'])
export const enum_onehealth_lab_orders_status = pgEnum("enum_onehealth_lab_orders_status", ['PENDING', 'PAYMENT_PENDING', 'COMPLETE_COLLECTION', 'COMPLETED'])
export const enum_prescription_preference_preference = pgEnum("enum_prescription_preference_preference", ['fax', 'life_file', 'doespot'])
export const enum_prescription_transfer_medications_status = pgEnum("enum_prescription_transfer_medications_status", ['pending', 'transferred'])
export const enum_promo_codes_code_type = pgEnum("enum_promo_codes_code_type", ['PROMO CODE', 'INVITE PROMO CODE', 'MD', 'LIFESTYLE', 'LAB', 'MD/LIFESTYLE/LAB'])
export const enum_promo_codes_discount_type = pgEnum("enum_promo_codes_discount_type", ['PERCENTAGE', 'DOLLAR'])
export const enum_promo_codes_usage_type = pgEnum("enum_promo_codes_usage_type", ['SINGLE', 'MULTIPLE'])
export const enum_referrals_status = pgEnum("enum_referrals_status", ['issued', 'canceled', 'pdf_pending'])
export const enum_requests_status = pgEnum("enum_requests_status", ['open', 'accepted', 'rejected'])
export const enum_stripe_user_details_cc_status = pgEnum("enum_stripe_user_details_cc_status", ['captured', 'not_captured', 'payment_error', 'capture_immediate'])
export const enum_stripe_user_details_oauth_status = pgEnum("enum_stripe_user_details_oauth_status", ['connected', 'not_connected', 'payouts_disabled'])
export const enum_stripe_user_payment_details_payment_status = pgEnum("enum_stripe_user_payment_details_payment_status", ['success', 'failed', 'action_required'])
export const enum_subscription_plans_billing_cycle = pgEnum("enum_subscription_plans_billing_cycle", ['monthly', 'yearly'])
export const enum_telehealth_service_questions_question_type = pgEnum("enum_telehealth_service_questions_question_type", ['YesNo', 'Text', 'Selection', 'MultipleSelection', 'Date', 'DateTime', 'TextArea', 'Height', 'Weight', 'Bmi', 'FileUpload'])
export const enum_tennant_master_preferred_payment_gateway = pgEnum("enum_tennant_master_preferred_payment_gateway", ['PAYPAL', 'STRIPE'])
export const enum_transactions_payment_method_type = pgEnum("enum_transactions_payment_method_type", ['STRIPE', 'BRAINTREE', 'PAYPAL'])
export const enum_transactions_payment_status = pgEnum("enum_transactions_payment_status", ['pending', 'completed', 'errored', 'cancelled'])
export const enum_transactions_refund_payment_status = pgEnum("enum_transactions_refund_payment_status", ['succeeded', 'failed', 'pending', 'n/a'])
export const enum_transactions_status = pgEnum("enum_transactions_status", ['succeeded', 'failed', 'pending'])
export const enum_transactions_transaction_status = pgEnum("enum_transactions_transaction_status", ['initiated', 'completed'])
export const enum_user_diet_meal_type = pgEnum("enum_user_diet_meal_type", ['breakfast', 'lunch', 'dinner', 'snacks'])
export const enum_user_file_repo_details_upload_type = pgEnum("enum_user_file_repo_details_upload_type", ['FAX', 'SFTP'])
export const enum_user_vitals_mode = pgEnum("enum_user_vitals_mode", ['automated', 'doctor', 'patient'])
export const enum_users_gender = pgEnum("enum_users_gender", ['male', 'female', 'others', 'transgender-female', 'transgender-male'])
export const enum_users_role = pgEnum("enum_users_role", ['USER', 'BUSER', 'AUSER', 'medical_assistant', 'viewer', 'support_user', 'pharmacist', 'PHARMACY', 'GROUP_ADMIN', 'SUPPORT_ADMIN', 'DEVELOPER'])
export const enum_users_status = pgEnum("enum_users_status", ['AVAILABLE', 'BUSY', 'AWAY', 'OFFLINE', 'ACTIVATION_PENDING', 'ONBOARDING_PENDING', 'PROFILE_INCOMPLETE'])
export const enum_users_sub_role = pgEnum("enum_users_sub_role", ['USER', 'DIETICIAN_NUTRITION', 'MENTAL_HEALTH', 'WEIGHT_LOSS_MANAGEMENT', 'DIABETES_PREVENTION'])
export const enum_visit_summary_upload_status_upload_status = pgEnum("enum_visit_summary_upload_status_upload_status", ['SUCCESS', 'FAILED'])
export const enum_visit_summary_upload_status_upload_type = pgEnum("enum_visit_summary_upload_status_upload_type", ['FAX', 'SFTP'])
export const enum_vitals_summary_upload_status_upload_status = pgEnum("enum_vitals_summary_upload_status_upload_status", ['SUCCESS', 'FAILED'])
export const enum_vitals_summary_upload_status_upload_type = pgEnum("enum_vitals_summary_upload_status_upload_type", ['FAX', 'SFTP'])
export const enum_webhooks_log_status = pgEnum("enum_webhooks_log_status", ['pending', 'success', 'failed'])


export const SequelizeMeta = pgTable("SequelizeMeta", {
	name: varchar("name", { length: 255 }).primaryKey().notNull(),
});

export const auth_provider = pgTable("auth_provider", {
	auth_id: serial("auth_id").notNull(),
	user_id: integer("user_id").references(() => users.user_id),
	json_token_id: text("json_token_id"),
	refresh_token: text("refresh_token"),
	iat: timestamp("iat", { withTimezone: true, mode: 'string' }),
	is_jti_valid: boolean("is_jti_valid").default(false),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const billing = pgTable("billing", {
	plan_id: serial("plan_id").notNull(),
	plan_name: text("plan_name"),
	plan_description: text("plan_description"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const contact_us = pgTable("contact_us", {
	contact_us_id: serial("contact_us_id").primaryKey().notNull(),
	user_id: integer("user_id").notNull(),
	date_requested: timestamp("date_requested", { withTimezone: true, mode: 'string' }),
	title: varchar("title", { length: 1000 }).default(''::character varying).notNull(),
	question: varchar("question", { length: 1000 }).default(''::character varying).notNull(),
	queryText: varchar("queryText", { length: 4000 }).default(''::character varying).notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const consult_notes = pgTable("consult_notes", {
	id: serial("id").primaryKey().notNull(),
	order_id: integer("order_id").notNull().references(() => orders.id),
	icd_code: text("icd_code").default(''),
	cpt_code: text("cpt_code").default(''),
	subjective: text("subjective").default(''),
	objective: text("objective").default(''),
	assessment: text("assessment").default(''),
	plan: text("plan").default(''),
	erm_id: varchar("erm_id", { length: 150 }).default(''::character varying),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	intervention: text("intervention"),
	outcome: text("outcome"),
	goal: text("goal"),
	order_guid: varchar("order_guid", { length: 50 }).references(() => telehealth_service_order.order_guid),
	general_note: varchar("general_note", { length: 2000 }).default(NULL::character varying),
});

export const affiliate_pharmacy = pgTable("affiliate_pharmacy", {
	id: serial("id").primaryKey().notNull(),
	pharmacy_name: varchar("pharmacy_name", { length: 250 }).default(''::character varying),
	street_one: varchar("street_one", { length: 255 }).default(''::character varying),
	street_two: varchar("street_two", { length: 255 }).default(''::character varying),
	city: varchar("city", { length: 75 }).default(''::character varying),
	state: varchar("state", { length: 50 }).default(''::character varying),
	zip_code: varchar("zip_code", { length: 20 }).default(''::character varying),
	country: varchar("country", { length: 50 }).default(''::character varying),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	email: varchar("email", { length: 200 }),
	fax_number: varchar("fax_number", { length: 20 }),
	phone_number: varchar("phone_number", { length: 20 }),
});

export const educational_videos = pgTable("educational_videos", {
	video_id: serial("video_id").primaryKey().notNull(),
	session_id: text("session_id"),
	archive_id: text("archive_id"),
	title: text("title").notNull(),
	description: text("description"),
	url: text("url"),
	created_by: integer("created_by").references(() => users.user_id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	audio_stream_screenshot: text("audio_stream_screenshot"),
});

export const encounters_values = pgTable("encounters_values", {
	value_id: serial("value_id").primaryKey().notNull(),
	summary_id: integer("summary_id").notNull().references(() => user_health_summary.summary_id),
	key: varchar("key", { length: 255 }),
	value: text("value"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const requests = pgTable("requests", {
	request_id: serial("request_id").primaryKey().notNull(),
	requestor_id: integer("requestor_id").notNull().references(() => users.user_id),
	requestee_id: integer("requestee_id").notNull().references(() => users.user_id),
	object_id: integer("object_id").notNull().references(() => request_objects.object_id),
	status: enum_requests_status("status").default('open'),
	message: varchar("message", { length: 255 }),
	detail: text("detail"),
	patient_history: text("patient_history"),
	requestor_read_status: boolean("requestor_read_status").default(false),
	requestee_read_status: boolean("requestee_read_status").default(false),
	entity_id: integer("entity_id"),
	add_practice_group_doctors: boolean("add_practice_group_doctors").default(false),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	order_guid: varchar("order_guid", { length: 50 }).default(NULL::character varying).references(() => telehealth_service_order.order_guid),
	release_medical: boolean("release_medical").default(false),
	rescheduled: boolean("rescheduled").default(false),
});

export const encryption_keys = pgTable("encryption_keys", {
	encryption_key_id: serial("encryption_key_id").primaryKey().notNull(),
	user_id: integer("user_id").notNull().references(() => users.user_id),
	device_id: integer("device_id").default(1).notNull(),
	registration_id: integer("registration_id").notNull(),
	identity_key: text("identity_key"),
	signed_pre_key: text("signed_pre_key"),
	pre_key: text("pre_key"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const health_summaries_schedule = pgTable("health_summaries_schedule", {
	schedule_id: serial("schedule_id").primaryKey().notNull(),
	user_id: integer("user_id").notNull().references(() => users.user_id),
	start_date: timestamp("start_date", { withTimezone: true, mode: 'string' }),
	end_date: timestamp("end_date", { withTimezone: true, mode: 'string' }),
	next_run_date: timestamp("next_run_date", { withTimezone: true, mode: 'string' }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const precanned_messages = pgTable("precanned_messages", {
	message_id: serial("message_id").primaryKey().notNull(),
	message: text("message").notNull(),
	type: varchar("type", { length: 255 }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const feed = pgTable("feed", {
	feed_id: serial("feed_id").primaryKey().notNull(),
	description: text("description"),
	address: text("address"),
	image_url: text("image_url"),
	date: timestamp("date", { withTimezone: true, mode: 'string' }),
	metadata: text("metadata"),
	user_id: integer("user_id").references(() => users.user_id),
	created_by: integer("created_by").references(() => users.user_id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const forms = pgTable("forms", {
	form_id: serial("form_id").primaryKey().notNull(),
	name: text("name").notNull(),
	url: text("url"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const promo_codes = pgTable("promo_codes", {
	code_id: serial("code_id").primaryKey().notNull(),
	code_type: enum_promo_codes_code_type("code_type").notNull(),
	service_id: integer("service_id").references(() => telehealth_services.id),
	lab_id: integer("lab_id"),
	discount_type: enum_promo_codes_discount_type("discount_type"),
	discount_values: text("discount_values"),
	promo_code: text("promo_code"),
	start_date: timestamp("start_date", { withTimezone: true, mode: 'string' }),
	end_date: timestamp("end_date", { withTimezone: true, mode: 'string' }),
	usage_type: enum_promo_codes_usage_type("usage_type"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	is_deleted: boolean("is_deleted").default(false),
	created_by: integer("created_by"),
	updated_by: integer("updated_by"),
	deleted_by: integer("deleted_by"),
});

export const drugs = pgTable("drugs", {
	drug_id: serial("drug_id").primaryKey().notNull(),
	category_id: integer("category_id").references(() => drugs_category.category_id),
	drug_full_name: varchar("drug_full_name", { length: 200 }).notNull(),
	tier: varchar("tier", { length: 200 }).notNull(),
	price: varchar("price", { length: 200 }).notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
	quantity: varchar("quantity", { length: 200 }),
});

export const invitations = pgTable("invitations", {
	invitation_id: serial("invitation_id").primaryKey().notNull(),
	invitor_id: integer("invitor_id").notNull().references(() => users.user_id),
	install_type: varchar("install_type", { length: 255 }),
	email: varchar("email", { length: 255 }),
	phone: varchar("phone", { length: 255 }),
	accepted: boolean("accepted").default(false),
	add_practice_group_doctors: boolean("add_practice_group_doctors").default(false),
	code: varchar("code", { length: 255 }),
	expiry: timestamp("expiry", { withTimezone: true, mode: 'string' }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const login_requests = pgTable("login_requests", {
	login_request_id: serial("login_request_id").primaryKey().notNull(),
	user_id: integer("user_id").references(() => users.user_id),
	bad_request: boolean("bad_request").default(false),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const favourite_drugs = pgTable("favourite_drugs", {
	drug_id: serial("drug_id").primaryKey().notNull(),
	user_id: integer("user_id").notNull().references(() => users.user_id),
	drug_name: varchar("drug_name", { length: 255 }).notNull(),
	brand: varchar("brand", { length: 255 }).notNull(),
	form: varchar("form", { length: 255 }).notNull(),
	dosage: varchar("dosage", { length: 255 }).notNull(),
	quantity: varchar("quantity", { length: 255 }).notNull(),
	refill_quantity: varchar("refill_quantity", { length: 255 }).notNull(),
	favourite: boolean("favourite").default(true).notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
	direction_quantity: varchar("direction_quantity", { length: 255 }),
	direction_one: varchar("direction_one", { length: 255 }),
	direction_two: varchar("direction_two", { length: 255 }),
	tennant_id: varchar("tennant_id", { length: 255 }),
	pharmacy_name: varchar("pharmacy_name", { length: 255 }),
	quantity_unit: varchar("quantity_unit", { length: 100 }),
	erx_product_id: varchar("erx_product_id", { length: 200 }),
	display_order: integer("display_order"),
	comments: text("comments"),
});

export const micromerchant_users = pgTable("micromerchant_users", {
	mm_user_id: serial("mm_user_id").primaryKey().notNull(),
	first_name: text("first_name"),
	last_name: text("last_name"),
	zip_code: varchar("zip_code", { length: 255 }),
	email: text("email").notNull(),
	phone: varchar("phone", { length: 255 }),
	gender: varchar("gender", { length: 255 }),
	data: text("data"),
	dob: text("dob"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const immunizations_values = pgTable("immunizations_values", {
	value_id: serial("value_id").primaryKey().notNull(),
	summary_id: integer("summary_id").notNull().references(() => user_health_summary.summary_id),
	key: varchar("key", { length: 255 }),
	value: text("value"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const medications_values = pgTable("medications_values", {
	value_id: serial("value_id").primaryKey().notNull(),
	summary_id: integer("summary_id").notNull().references(() => user_health_summary.summary_id),
	key: varchar("key", { length: 255 }),
	value: text("value"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const mms_patients = pgTable("mms_patients", {
	patient_id: serial("patient_id").primaryKey().notNull(),
	patient_no: varchar("patient_no", { length: 255 }).notNull(),
	pharmacy_id: integer("pharmacy_id").references(() => pharmacies.pharmacy_id),
	user_id: integer("user_id").references(() => users.user_id),
	first_name: varchar("first_name", { length: 255 }),
	last_name: varchar("last_name", { length: 255 }),
	gender: varchar("gender", { length: 255 }),
	phone: varchar("phone", { length: 255 }),
	email: varchar("email", { length: 255 }),
	active: varchar("active", { length: 255 }),
	address: text("address"),
	notes: text("notes"),
	payment_preference: text("payment_preference"),
	messaging_settings: text("messaging_settings"),
	diagnostics: text("diagnostics"),
	allergies: text("allergies"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	insurances: text("insurances"),
	source: varchar("source", { length: 255 }),
	dob: text("dob"),
	is_smoker: boolean("is_smoker").default(false),
	marital_status: varchar("marital_status", { length: 20 }),
	weight: numeric("weight", { precision: 10, scale:  3 }),
	is_pregnant: boolean("is_pregnant").default(false),
	height: numeric("height", { precision: 10, scale:  3 }),
	medical_record_number: varchar("medical_record_number", { length: 50 }),
	species_type: varchar("species_type", { length: 15 }),
	dea_restriction_code: varchar("dea_restriction_code", { length: 50 }),
	family_email: varchar("family_email", { length: 255 }),
	patient_remark: varchar("patient_remark", { length: 255 }),
	patient_short_remark: varchar("patient_short_remark", { length: 255 }),
	family_remark: varchar("family_remark", { length: 255 }),
	race: varchar("race", { length: 30 }),
	work_phone: varchar("work_phone", { length: 15 }),
	mobile: varchar("mobile", { length: 15 }),
	chart_no: varchar("chart_no", { length: 30 }),
	language: varchar("language", { length: 30 }),
	ez_cap: boolean("ez_cap").default(false),
	discount_code: varchar("discount_code", { length: 30 }),
	short_sode: varchar("short_sode", { length: 30 }),
	price_code_brand: varchar("price_code_brand", { length: 30 }),
	price_code_generic: varchar("price_code_generic", { length: 30 }),
	charge_account: varchar("charge_account", { length: 30 }),
	print_drug_counselling: varchar("print_drug_counselling", { length: 30 }),
	category: varchar("category", { length: 30 }),
	preferred_delivery_method: varchar("preferred_delivery_method", { length: 50 }),
	driver_license_number: varchar("driver_license_number", { length: 50 }),
	hippa_signature: varchar("hippa_signature", { length: 50 }),
	driver_license_expiry: varchar("driver_license_expiry", { length: 50 }),
});

export const mms_request_payload = pgTable("mms_request_payload", {
	id: integer("id").primaryKey().notNull(),
	rxno: varchar("rxno", { length: 50 }).notNull(),
	pharmacy_system: varchar("pharmacy_system", { length: 50 }).notNull(),
	pharmacy_token: varchar("pharmacy_token", { length: 50 }),
	payload: text("payload"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const mms_prescriptions = pgTable("mms_prescriptions", {
	prescription_id: serial("prescription_id").primaryKey().notNull(),
	patient_id: integer("patient_id").notNull().references(() => mms_patients.patient_id),
	rx_no: varchar("rx_no", { length: 255 }),
	auth_refills: varchar("auth_refills", { length: 255 }),
	drug_info: text("drug_info"),
	qty_ordered: numeric("qty_ordered"),
	date_ordered: varchar("date_ordered", { length: 255 }),
	date_expires: varchar("date_expires", { length: 255 }),
	discontinued: varchar("discontinued", { length: 255 }),
	sig: varchar("sig", { length: 255 }),
	prescriber: text("prescriber"),
	diagnostics: text("diagnostics"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	auth: varchar("auth", { length: 50 }),
	source_pms: varchar("source_pms", { length: 50 }),
	refill_no: varchar("refill_no", { length: 50 }),
	pharmacy_id: integer("pharmacy_id"),
	date_filled: varchar("date_filled", { length: 50 }),
	discontinue_reason: varchar("discontinue_reason", { length: 50 }),
	date_discontinued: varchar("date_discontinued", { length: 50 }),
	status: varchar("status", { length: 50 }),
	days_supplied: varchar("days_supplied", { length: 50 }),
	rx_serial_no: varchar("rx_serial_no", { length: 50 }),
	bill_type: varchar("bill_type", { length: 50 }),
	bill_as: varchar("bill_as", { length: 50 }),
	qty_dispensed: varchar("qty_dispensed", { length: 50 }),
	delivery_method: varchar("delivery_method", { length: 50 }),
	hold_rx: varchar("hold_rx", { length: 50 }),
	rph: varchar("rph", { length: 50 }),
	date_picked: varchar("date_picked", { length: 30 }),
	awp: varchar("awp", { length: 50 }),
	time_picked: varchar("time_picked", { length: 30 }),
	picked_up: varchar("picked_up", { length: 50 }),
	prescription_notes: varchar("prescription_notes", { length: 255 }),
	delivery_date: varchar("delivery_date", { length: 30 }),
	tracking_url: varchar("tracking_url", { length: 50 }),
	shipping_service: varchar("shipping_service", { length: 50 }),
	source_script_id: varchar("source_script_id", { length: 50 }),
	flag340b: varchar("flag340b", { length: 50 }),
	daw_code: varchar("daw_code", { length: 50 }),
	dispense_as_written: varchar("dispense_as_written", { length: 50 }),
	source_script_message: varchar("source_script_message", { length: 50 }),
	cost_price: varchar("cost_price", { length: 50 }),
	rx_amount: varchar("rx_amount", { length: 50 }),
	disp_fee: varchar("disp_fee", { length: 50 }),
	dosage_fields: text("dosage_fields"),
	patient_copay: varchar("patient_copay", { length: 50 }),
	billed: varchar("billed", { length: 50 }),
	primary_insurance: text("primary_insurance"),
	fill_list_indicator: varchar("fill_list_indicator", { length: 50 }),
	submission_clar_code: varchar("submission_clar_code", { length: 50 }),
	horizon_graveyard_code: varchar("horizon_graveyard_code", { length: 50 }),
	room_number: varchar("room_number", { length: 10 }),
	nursing_home_id: varchar("nursing_home_id", { length: 30 }),
	location_code: varchar("location_code", { length: 20 }),
	facility_code: varchar("facility_code", { length: 20 }),
	nursing_home: varchar("nursing_home", { length: 50 }),
	wing_code1: varchar("wing_code1", { length: 50 }),
	refering_doctor: varchar("refering_doctor", { length: 50 }),
	xfer_to_pharmacy_name: varchar("xfer_to_pharmacy_name", { length: 50 }),
	wing_code2: varchar("wing_code2", { length: 50 }),
	xfer_to_pharmacy_address1: varchar("xfer_to_pharmacy_address1", { length: 255 }),
	xfer_to_pharmacy_address2: varchar("xfer_to_pharmacy_address2", { length: 255 }),
	xfer_to_pharmacy_city: varchar("xfer_to_pharmacy_city", { length: 50 }),
	xfer_to_pharmacy_phone: varchar("xfer_to_pharmacy_phone", { length: 20 }),
	xfer_to_pharmacy_npi: varchar("xfer_to_pharmacy_npi", { length: 30 }),
	xfer_to_pharmacy_ncpdp: varchar("xfer_to_pharmacy_ncpdp", { length: 30 }),
	bill_status: varchar("bill_status", { length: 50 }),
	xfer_to_pharmacy_dea: varchar("xfer_to_pharmacy_dea", { length: 30 }),
	bill_status_text: varchar("bill_status_text", { length: 50 }),
	workflow_status: varchar("workflow_status", { length: 50 }),
	prescribed_drug: varchar("prescribed_drug", { length: 50 }),
	workflow_status_text: varchar("workflow_status_text", { length: 50 }),
	claim_authorization_number: varchar("claim_authorization_number", { length: 50 }),
	prior_auth_number: varchar("prior_auth_number", { length: 50 }),
	election_prescription_origin_time: varchar("election_prescription_origin_time", { length: 50 }),
});

export const onehealth_lab_orders = pgTable("onehealth_lab_orders", {
	id: serial("id").primaryKey().notNull(),
	order_guid: varchar("order_guid", { length: 50 }),
	user_id: integer("user_id").references(() => users.user_id),
	registered_kit_id: varchar("registered_kit_id", { length: 50 }),
	testing_kit_type: varchar("testing_kit_type", { length: 100 }).default(''::character varying),
	total_quantity: integer("total_quantity").default(1).notNull(),
	use_prescription_service: boolean("use_prescription_service").default(false),
	interval: varchar("interval", { length: 30 }),
	results_needs_revision: boolean("results_needs_revision").default(false),
	lab: varchar("lab", { length: 30 }).default('rucdr'::character varying),
	additional_data: varchar("additional_data", { length: 30 }).default('rucdr'::character varying),
	posted_data: text("posted_data"),
	company: varchar("company", { length: 30 }).default('Ravkoo'::character varying),
	return_mailer: varchar("return_mailer", { length: 255 }).default(''::character varying),
	insurance_enabled: varchar("insurance_enabled", { length: 255 }).default(''::character varying),
	kit_ids: varchar("kit_ids", { length: 255 }).default(''::character varying),
	patient_id: varchar("patient_id", { length: 50 }).default(''::character varying),
	hcp_id: varchar("hcp_id", { length: 50 }).default(''::character varying),
	test_code: varchar("test_code", { length: 50 }).default(''::character varying),
	askOnEntry: varchar("askOnEntry", { length: 50 }).default(''::character varying),
	diagnostic_code: varchar("diagnostic_code", { length: 255 }).default(''::character varying),
	diagnose_observation_date: timestamp("diagnose_observation_date", { withTimezone: true, mode: 'string' }),
	lab_ref_id: varchar("lab_ref_id", { length: 255 }).default(''::character varying),
	status: enum_onehealth_lab_orders_status("status").default('PENDING'),
	payment_status: enum_onehealth_lab_orders_payment_status("payment_status").default('PENDING'),
	one_health: text("one_health"),
	collection_at: timestamp("collection_at", { withTimezone: true, mode: 'string' }),
	kit_register_at: timestamp("kit_register_at", { withTimezone: true, mode: 'string' }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	service_charges: numeric("service_charges").default('0'),
	shipping_amount: numeric("shipping_amount").default('0'),
	total_cost: numeric("total_cost").default('0'),
	lab_id: varchar("lab_id", { length: 30 }).default(NULL::character varying),
	payment_transaction: text("payment_transaction"),
	flat_price: numeric("flat_price").default('0'),
	name: varchar("name", { length: 150 }).default(''::character varying),
	product_code: varchar("product_code", { length: 100 }).default(''::character varying),
	discount: numeric("discount").default('0'),
},
(table) => {
	return {
		onehealth_lab_orders_order_guid_key: unique("onehealth_lab_orders_order_guid_key").on(table.order_guid),
	}
});

export const patient_insurances = pgTable("patient_insurances", {
	patient_insurance_id: serial("patient_insurance_id").primaryKey().notNull(),
	patient_id: integer("patient_id").notNull().references(() => mms_patients.patient_id),
	insurance_id: varchar("insurance_id", { length: 255 }),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});


export const permission_groups = pgTable("permission_groups", {
	patient_id: integer("patient_id").notNull().references(() => users.user_id),
	associated_user_id: integer("associated_user_id").notNull().references(() => users.user_id),
	group_id: serial("group_id").primaryKey().notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
},
(table) => {
	return {
		permission_groups_patient_id_associated_user_id_key: unique("permission_groups_patient_id_associated_user_id_key").on(table.patient_id, table.associated_user_id),
	}
});


export const practice_groups = pgTable("practice_groups", {
	practice_group_id: serial("practice_group_id").primaryKey().notNull(),
	name: text("name"),
	phones: text("phones"),
	visit_address: text("visit_address"),
	install_type: varchar("install_type", { length: 255 }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const mobile_number_otp_validator = pgTable("mobile_number_otp_validator", {
	id: uuid("id").primaryKey().notNull(),
	phone: varchar("phone", { length: 20 }).notNull(),
	otp: varchar("otp", { length: 1000 }).notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const requests_log = pgTable("requests_log", {
	request_log_id: serial("request_log_id").primaryKey().notNull(),
	user_id: integer("user_id").references(() => users.user_id),
	install_type: varchar("install_type", { length: 255 }),
	method: varchar("method", { length: 255 }).notNull(),
	response_http_status: integer("response_http_status").notNull(),
	endpoint: text("endpoint").notNull(),
	timestamp: timestamp("timestamp", { withTimezone: true, mode: 'string' }).notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const consult_update_details_history = pgTable("consult_update_details_history", {
	id: serial("id").primaryKey().notNull(),
	service_order_id: integer("service_order_id").notNull().references(() => telehealth_service_order.id),
	user_id: integer("user_id").notNull().references(() => users.user_id),
	previous_values: varchar("previous_values", { length: 2000 }),
	updated_values: varchar("updated_values", { length: 2000 }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
});

export const chat_room_members = pgTable("chat_room_members", {
	id: serial("id").primaryKey().notNull(),
	user_id: serial("user_id").notNull().references(() => users.user_id),
	room_id: serial("room_id").notNull().references(() => chat_rooms.id),
	deleted: boolean("deleted").default(false).notNull(),
	deleted_at: timestamp("deleted_at", { mode: 'string' }),
	created_at: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updated_at: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const chat_rooms = pgTable("chat_rooms", {
	id: serial("id").primaryKey().notNull(),
	room_name: text("room_name").notNull(),
	room_identifier: uuid("room_identifier").defaultRandom(),
	deleted: boolean("deleted").default(false).notNull(),
	deleted_at: timestamp("deleted_at", { mode: 'string' }),
	created_at: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updated_at: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	service_key: text("service_key"),
	description: text("description"),
});

export const request_objects = pgTable("request_objects", {
	object_id: serial("object_id").primaryKey().notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	type: varchar("type", { length: 255 }).notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const medicines = pgTable("medicines", {
	medicine_id: serial("medicine_id").primaryKey().notNull(),
	name: varchar("name", { length: 50 }).notNull(),
	description: text("description"),
	brand: varchar("brand", { length: 500 }),
	form: varchar("form", { length: 500 }),
	dosage: varchar("dosage", { length: 500 }),
	quantity: varchar("quantity", { length: 500 }),
	refill_quantity: varchar("refill_quantity", { length: 500 }),
	direction_quantity: varchar("direction_quantity", { length: 500 }),
	direction_one: varchar("direction_one", { length: 500 }),
	direction_two: varchar("direction_two", { length: 500 }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
});

export const medicine_service_pharmacy_mapping = pgTable("medicine_service_pharmacy_mapping", {
	id: serial("id").primaryKey().notNull(),
	medicine_id: integer("medicine_id").notNull().references(() => medicines.medicine_id),
	pharmacy_id: integer("pharmacy_id").notNull().references(() => pharmacies.pharmacy_id),
	service_id: integer("service_id").notNull().references(() => telehealth_services.id),
	status: boolean("status").default(true),
});

export const transactions = pgTable("transactions", {
	transaction_id: text("transaction_id").primaryKey().notNull(),
	payer_user_id: integer("payer_user_id").notNull().references(() => users.user_id),
	payee_user_id: integer("payee_user_id").notNull().references(() => users.user_id),
	amount: doublePrecision("amount").notNull(),
	currency: text("currency").notNull(),
	status: enum_transactions_status("status").notNull(),
	description: text("description"),
	error_description: text("error_description"),
	card_details: text("card_details"),
	line_items: text("line_items"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	global_id: varchar("global_id", { length: 100 }),
	refund_transaction_id: varchar("refund_transaction_id", { length: 100 }),
	refund_created_at: timestamp("refund_created_at", { withTimezone: true, mode: 'string' }),
	refund_global_id: varchar("refund_global_id", { length: 100 }),
	order_guid: varchar("order_guid", { length: 50 }).default(NULL::character varying),
	payment_method_type: enum_transactions_payment_method_type("payment_method_type").default('BRAINTREE'),
	refund_payment_status: enum_transactions_refund_payment_status("refund_payment_status").default('n/a'),
	refund_payment_amount: doublePrecision("refund_payment_amount"),
	refund_error_description: text("refund_error_description"),
	refund_success_response: text("refund_success_response"),
	transaction_status: varchar("transaction_status", { length: 100 }),
});

export const special_discounts = pgTable("special_discounts", {
	discount_id: serial("discount_id").primaryKey().notNull(),
	name: text("name").notNull(),
	description: text("description"),
	discount_price: integer("discount_price").notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const stripe_user_payment_details = pgTable("stripe_user_payment_details", {
	id: serial("id").primaryKey().notNull(),
	user_id: integer("user_id").notNull().references(() => users.user_id),
	payment_method_id: text("payment_method_id"),
	off_session_payment_allowed: boolean("off_session_payment_allowed"),
	payment_status: enum_stripe_user_payment_details_payment_status("payment_status").default('success').notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const subscription_plans = pgTable("subscription_plans", {
	plan_id: serial("plan_id").primaryKey().notNull(),
	name: text("name").notNull(),
	description: text("description"),
	price: integer("price").notNull(),
	currency: text("currency").notNull(),
	billing_cycle: enum_subscription_plans_billing_cycle("billing_cycle").notNull(),
	billing_interval: integer("billing_interval").notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	paypal_plan_id: text("paypal_plan_id"),
	service_id: integer("service_id"),
});

export const tennant_master = pgTable("tennant_master", {
	id: serial("id").primaryKey().notNull(),
	tennant_name: varchar("tennant_name", { length: 255 }).notNull(),
	pharmacy_fax: varchar("pharmacy_fax", { length: 255 }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
	filter_display: boolean("filter_display").default(false),
	parent_id: integer("parent_id"),
	lab_fax_number: varchar("lab_fax_number", { length: 255 }),
	support_email: varchar("support_email", { length: 255 }),
	logo_url: varchar("logo_url", { length: 255 }),
	paypal_client_id: text("paypal_client_id"),
	paypal_client_secret: text("paypal_client_secret"),
	access_lab_client_number: varchar("access_lab_client_number", { length: 2000 }),
	twilio_account_sid: text("twilio_account_sid"),
	twilio_phone_number: text("twilio_phone_number"),
	twilio_auth_token: text("twilio_auth_token"),
	stripe_client_id: varchar("stripe_client_id", { length: 2000 }),
	stripe_client_secret: varchar("stripe_client_secret", { length: 2000 }),
	preferred_payment_gateway: enum_tennant_master_preferred_payment_gateway("preferred_payment_gateway"),
	mailchip_from_email: varchar("mailchip_from_email", { length: 255 }),
	mailchip_from_email_name: varchar("mailchip_from_email_name", { length: 255 }),
	tennant_display_name: varchar("tennant_display_name", { length: 255 }),
	tenant_access: varchar("tenant_access", { length: 500 }),
	dosespot_client_id: varchar("dosespot_client_id", { length: 1000 }),
	dosespot_client_secret: varchar("dosespot_client_secret", { length: 1000 }),
},
(table) => {
	return {
		tennant_master_parent_id_fkey: foreignKey({
			columns: [table.parent_id],
			foreignColumns: [table.id],
			name: "tennant_master_parent_id_fkey"
		}),
	}
});

export const telehealth_service_questions = pgTable("telehealth_service_questions", {
	id: serial("id").primaryKey().notNull(),
	service_id: integer("service_id").references(() => telehealth_services.id),
	question: varchar("question", { length: 500 }).notNull(),
	help_text: varchar("help_text", { length: 200 }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	selection_option: text("selection_option"),
	question_type: enum_telehealth_service_questions_question_type("question_type").default('YesNo'),
	display_order: integer("display_order").default(1),
	parent_id: integer("parent_id"),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
	halt_on_selection_option: varchar("halt_on_selection_option", { length: 500 }),
	is_pre_questions: boolean("is_pre_questions").default(false),
});

export const telehealth_service_provider_mapping = pgTable("telehealth_service_provider_mapping", {
	id: serial("id").primaryKey().notNull(),
	service_id: integer("service_id").references(() => telehealth_services.id),
	provider_id: integer("provider_id").references(() => users.user_id),
	cost_price: numeric("cost_price").default('0'),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	status: boolean("status").default(true),
});

export const stripe_user_details = pgTable("stripe_user_details", {
	user_id: integer("user_id").primaryKey().notNull().references(() => users.user_id),
	stripe_user_id: text("stripe_user_id").notNull(),
	cc_status: enum_stripe_user_details_cc_status("cc_status").default('not_captured'),
	oauth_status: enum_stripe_user_details_oauth_status("oauth_status").default('not_connected'),
	stripe_account_id: text("stripe_account_id"),
	oauth_verification_token: text("oauth_verification_token"),
	currency_code: text("currency_code"),
	default_payment_method: text("default_payment_method"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const social_history_values = pgTable("social_history_values", {
	value_id: serial("value_id").primaryKey().notNull(),
	summary_id: integer("summary_id").notNull().references(() => user_health_summary.summary_id),
	key: varchar("key", { length: 255 }),
	value: text("value"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const user_particlehealth = pgTable("user_particlehealth", {
	health_id: serial("health_id").primaryKey().notNull(),
	user_id: integer("user_id").notNull().references(() => users.user_id),
	query_id: varchar("query_id", { length: 255 }),
	status: varchar("status", { length: 255 }),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	buser_id: integer("buser_id").notNull().references(() => users.user_id),
});

export const user_educational_videos = pgTable("user_educational_videos", {
	user_video_id: serial("user_video_id").primaryKey().notNull(),
	video_id: serial("video_id").notNull().references(() => educational_videos.video_id),
	referred_by: integer("referred_by").notNull().references(() => users.user_id),
	referred_for: integer("referred_for").notNull().references(() => users.user_id),
	doctor_id: integer("doctor_id").references(() => users.user_id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	viewed_at: timestamp("viewed_at", { withTimezone: true, mode: 'string' }),
	viewed: boolean("viewed"),
});

export const user_details = pgTable("user_details", {
	user_detail_id: serial("user_detail_id").primaryKey().notNull(),
	user_id: integer("user_id").references(() => users.user_id),
	data: text("data"),
	signature: varchar("signature", { length: 255 }),
	specialty: varchar("specialty", { length: 255 }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	address: text("address"),
	deleted: boolean("deleted").default(false),
});

export const products = pgTable("products", {
	product_id: serial("product_id").primaryKey().notNull(),
	product_name: varchar("product_name", { length: 200 }).notNull(),
	description: varchar("description", { length: 2000 }).notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
});

export const user_identities = pgTable("user_identities", {
	user_identity_id: serial("user_identity_id").primaryKey().notNull(),
	user_id: integer("user_id").references(() => users.user_id),
	identifier: text("identifier"),
	type: varchar("type", { length: 255 }),
	install_type: varchar("install_type", { length: 255 }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const follow_up_reminder = pgTable("follow_up_reminder", {
	id: serial("id").primaryKey().notNull(),
	order_id: integer("order_id").references(() => telehealth_service_order.id),
	user_id: integer("user_id").references(() => users.user_id),
	last_follow_up_sent: varchar("last_follow_up_sent", { length: 50 }),
	error_message: varchar("error_message", { length: 200 }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
	next_follow_up: timestamp("next_follow_up", { withTimezone: true, mode: 'string' }),
	status: enum_follow_up_reminder_status("status").default('pending'),
	tennant_id: integer("tennant_id").references(() => tennant_master.id),
});

export const user_file_repo_details = pgTable("user_file_repo_details", {
	repo_id: serial("repo_id").primaryKey().notNull(),
	user_id: integer("user_id").notNull().references(() => users.user_id),
	upload_type: enum_user_file_repo_details_upload_type("upload_type").notNull(),
	connection_details: text("connection_details"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const user_health_summary = pgTable("user_health_summary", {
	summary_id: serial("summary_id").primaryKey().notNull(),
	user_id: integer("user_id").notNull().references(() => users.user_id),
	type: varchar("type", { length: 255 }),
	data: text("data"),
	source_platform: varchar("source_platform", { length: 255 }),
	has_detail: boolean("has_detail").default(false),
	date: timestamp("date", { withTimezone: true, mode: 'string' }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const user_subscription_billing = pgTable("user_subscription_billing", {
	billing_id: serial("billing_id").primaryKey().notNull(),
	user_id: integer("user_id").notNull().references(() => users.user_id),
	plan_id: integer("plan_id").notNull().references(() => subscription_plans.plan_id),
	base_plan_price: integer("base_plan_price").notNull(),
	discount: integer("discount").notNull(),
	invoice_number: text("invoice_number"),
	special_discounts: text("special_discounts"),
	addons: text("addons"),
	date: timestamp("date", { withTimezone: true, mode: 'string' }),
	total_price: text("total_price").default(0),
	billed: boolean("billed").default(false),
	invoice_pdf_path: text("invoice_pdf_path"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const drug_days = pgTable("drug_days", {
	id: serial("id").primaryKey().notNull(),
	drug_id: integer("drug_id").references(() => drugs.drug_id),
	drug_days: integer("drug_days").notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
});

export const consult_order_files = pgTable("consult_order_files", {
	file_id: serial("file_id").primaryKey().notNull(),
	order_id: integer("order_id").references(() => telehealth_service_order.id),
	type: enum_consult_order_files_type("type"),
	file_path: text("file_path").notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
	tennant_id: integer("tennant_id").references(() => tennant_master.id),
});

export const user_subscription = pgTable("user_subscription", {
	user_subscription_id: serial("user_subscription_id").primaryKey().notNull(),
	user_id: integer("user_id").notNull().references(() => users.user_id),
	plan_id: integer("plan_id").notNull().references(() => subscription_plans.plan_id),
	start_date: timestamp("start_date", { withTimezone: true, mode: 'string' }).notNull(),
	no_of_intervals: integer("no_of_intervals"),
	referral_code: text("referral_code"),
	discount: integer("discount").default(0),
	next_billing_date: timestamp("next_billing_date", { withTimezone: true, mode: 'string' }),
	is_active: boolean("is_active").default(true),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	subscription_id: text("subscription_id"),
	status: text("status"),
	service_id: integer("service_id"),
});

export const chat_messages = pgTable("chat_messages", {
	id: serial("id").primaryKey().notNull(),
	message: text("message"),
	sender_id: serial("sender_id").notNull().references(() => users.user_id),
	room_id: serial("room_id").notNull().references(() => chat_rooms.id),
	deleted: boolean("deleted").default(false).notNull(),
	deleted_at: timestamp("deleted_at", { mode: 'string' }),
	created_at: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updated_at: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	file_id: integer("file_id"),
});

export const sms_template = pgTable("sms_template", {
	sms_template_id: serial("sms_template_id").primaryKey().notNull(),
	sms_action: varchar("sms_action", { length: 100 }).notNull(),
	sms_template_name: varchar("sms_template_name", { length: 500 }).notNull(),
	service_id: integer("service_id").references(() => telehealth_services.id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
});

export const user_vitals_documents = pgTable("user_vitals_documents", {
	user_vitals_document_id: serial("user_vitals_document_id").primaryKey().notNull(),
	user_id: integer("user_id").references(() => users.user_id),
	doctor_id: integer("doctor_id").references(() => users.user_id),
	path: text("path"),
	start_time: timestamp("start_time", { withTimezone: true, mode: 'string' }),
	end_time: timestamp("end_time", { withTimezone: true, mode: 'string' }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const jobs = pgTable("jobs", {
	id: serial("id").primaryKey().notNull(),
	path: text("path"),
	order_id: integer("order_id").notNull().references(() => telehealth_service_order.id),
	referral_id: integer("referral_id").notNull().references(() => referrals.referral_id),
	status: enum_jobs_status("status").default('pending'),
	failed_message: text("failed_message"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
});

export const chat_files = pgTable("chat_files", {
	file_id: serial("file_id").primaryKey().notNull(),
	user_id: serial("user_id").notNull().references(() => users.user_id),
	room_id: serial("room_id").notNull().references(() => chat_rooms.id),
	name: text("name"),
	path: text("path"),
	created_at: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updated_at: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const user_schedules = pgTable("user_schedules", {
	id: uuid("id").primaryKey().notNull(),
	user_id: integer("user_id").notNull().references(() => users.user_id),
	is_available: boolean("is_available").default(true),
	end_datetime: timestamp("end_datetime", { withTimezone: true, mode: 'string' }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
	created_by: integer("created_by").references(() => users.user_id),
	updated_by: integer("updated_by").references(() => users.user_id),
	deleted_by: integer("deleted_by").references(() => users.user_id),
	schedule_date: varchar("schedule_date", { length: 30 }).default(''::character varying).notNull(),
	start_datetime: timestamp("start_datetime", { withTimezone: true, mode: 'string' }),
});

export const email_template = pgTable("email_template", {
	email_template_id: serial("email_template_id").primaryKey().notNull(),
	email_action: varchar("email_action", { length: 100 }).notNull(),
	email_template_name: varchar("email_template_name", { length: 500 }).notNull(),
	service_id: integer("service_id").references(() => telehealth_services.id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
});

export const prescription_preference = pgTable("prescription_preference", {
	preference_id: serial("preference_id").primaryKey().notNull(),
	pharmacy_id: integer("pharmacy_id").references(() => pharmacies.pharmacy_id),
	tennant_id: integer("tennant_id").references(() => tennant_master.id),
	pharmacy_name: varchar("pharmacy_name", { length: 100 }),
	client_name: varchar("client_name", { length: 100 }),
	preference: enum_prescription_preference_preference("preference").default('fax'),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
});

export const lifefile_configuration = pgTable("lifefile_configuration", {
	lifefile_config_id: serial("lifefile_config_id").primaryKey().notNull(),
	tennant_id: integer("tennant_id").references(() => tennant_master.id),
	pharmacy_id: integer("pharmacy_id").references(() => pharmacies.pharmacy_id),
	pharmacy_name: varchar("pharmacy_name", { length: 100 }),
	client_name: varchar("client_name", { length: 100 }),
	lifefile_url: varchar("lifefile_url", { length: 200 }).notNull(),
	api_username: varchar("api_username", { length: 100 }).notNull(),
	api_password: varchar("api_password", { length: 100 }).notNull(),
	practice_id: integer("practice_id").notNull(),
	practice_name: varchar("practice_name", { length: 100 }).notNull(),
	vendor_id: integer("vendor_id").notNull(),
	location_id: integer("location_id").notNull(),
	network_id: integer("network_id").notNull(),
	network_name: varchar("network_name", { length: 100 }).notNull(),
	shipping_services: enum_lifefile_configuration_shipping_services("shipping_services").default(7780),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
});

export const users = pgTable("users", {
	user_id: serial("user_id").primaryKey().notNull(),
	user_guid: varchar("user_guid", { length: 255 }),
	password: text("password"),
	role: enum_users_role("role").default('USER'),
	status: enum_users_status("status").default('OFFLINE'),
	install_type: varchar("install_type", { length: 255 }),
	first_name: text("first_name"),
	last_name: text("last_name"),
	zip_code: varchar("zip_code", { length: 255 }),
	email: text("email").notNull(),
	phone: text("phone"),
	otp: text("otp"),
	dob: text("dob"),
	appt_length: integer("appt_length"),
	appt_start_time: varchar("appt_start_time", { length: 255 }),
	appt_end_time: varchar("appt_end_time", { length: 255 }),
	secure_message: boolean("secure_message").default(false),
	connection_requests: boolean("connection_requests").default(false),
	vitals_ccd_enabled: boolean("vitals_ccd_enabled").default(false),
	appt_requests: boolean("appt_requests").default(false),
	trial_validity: timestamp("trial_validity", { withTimezone: true, mode: 'string' }),
	is_cc_captured: boolean("is_cc_captured"),
	gender: enum_users_gender("gender").default('male'),
	deleted: boolean("deleted").default(false),
	email_verified: boolean("email_verified").default(false),
	user_avatar: text("user_avatar"),
	id_card: text("id_card"),
	history: text("history"),
	questionnaire: text("questionnaire"),
	token_validity: integer("token_validity"),
	email_verification_details: text("email_verification_details"),
	last_active: timestamp("last_active", { withTimezone: true, mode: 'string' }),
	locked: timestamp("locked", { withTimezone: true, mode: 'string' }),
	registration_key: text("registration_key"),
	is_rpm_enabled: boolean("is_rpm_enabled").default(false),
	is_notify_on_capture: boolean("is_notify_on_capture").default(false),
	vitals_thresholds: text("vitals_thresholds"),
	vitals_cron: text("vitals_cron"),
	app_details: text("app_details"),
	history_updated_at: timestamp("history_updated_at", { withTimezone: true, mode: 'string' }),
	questionnaire_updated_at: timestamp("questionnaire_updated_at", { withTimezone: true, mode: 'string' }),
	debug: boolean("debug").default(false),
	invitation_code_validity: integer("invitation_code_validity"),
	cron_expression: text("cron_expression"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	mirth_ccd_enabled: boolean("mirth_ccd_enabled").default(false),
	cc_payment_accepted: boolean("cc_payment_accepted").default(false),
	recording_enabled: boolean("recording_enabled").default(true),
	transcription_enabled: boolean("transcription_enabled").default(true),
	orders_enabled: boolean("orders_enabled").default(true),
	show_health_summaries: boolean("show_health_summaries"),
	healthgorilla_id: varchar("healthgorilla_id", { length: 255 }),
	release_medical: boolean("release_medical"),
	telehealth_service_cost: numeric("telehealth_service_cost").default('0'),
	sub_role: enum_users_sub_role("sub_role").default('USER'),
	my_invite_code: varchar("my_invite_code", { length: 15 }).default(NULL::character varying),
	referred_by_invite_code: varchar("referred_by_invite_code", { length: 15 }).default(NULL::character varying),
	referred_by_user_id: integer("referred_by_user_id"),
	email_2: text("email_2"),
	tennant_id: integer("tennant_id").references(() => tennant_master.id),
	secondary_phone: text("secondary_phone"),
	is_tennant_owner: boolean("is_tennant_owner").default(false).notNull(),
	send_email_campaign: boolean("send_email_campaign").default(false).notNull(),
	app_timezone: varchar("app_timezone", { length: 255 }).default('{}'::character varying),
	rxPersonId: varchar("rxPersonId", { length: 50 }),
	rxStatus: boolean("rxStatus").default(false),
	tenant_access: varchar("tenant_access", { length: 500 }),
	dosespot_api_response: varchar("dosespot_api_response", { length: 2000 }),
},
(table) => {
	return {
		users_my_invite_code_key: unique("users_my_invite_code_key").on(table.my_invite_code),
	}
});

export const telehealth_service_question_answer_dump = pgTable("telehealth_service_question_answer_dump", {
	id: serial("id").primaryKey().notNull(),
	service_order_id: integer("service_order_id").references(() => telehealth_service_order.id),
	question_text: varchar("question_text", { length: 2000 }).notNull(),
	answer: varchar("answer", { length: 2000 }).default(false),
	other_text: varchar("other_text", { length: 2000 }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
});

export const consult_reassign_history = pgTable("consult_reassign_history", {
	id: serial("id").primaryKey().notNull(),
	service_order_id: integer("service_order_id").notNull().references(() => telehealth_service_order.id),
	previous_provider: integer("previous_provider").notNull().references(() => users.user_id),
	updated_provider: integer("updated_provider").notNull().references(() => users.user_id),
	reassigned_by: integer("reassigned_by").notNull().references(() => users.user_id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
});

export const pharmacies = pgTable("pharmacies", {
	pharmacy_id: serial("pharmacy_id").primaryKey().notNull(),
	npi: varchar("npi", { length: 255 }).notNull(),
	name: varchar("name", { length: 255 }),
	nabp: varchar("nabp", { length: 255 }),
	address: text("address"),
	phone: varchar("phone", { length: 255 }),
	fax: varchar("fax", { length: 255 }),
	email: varchar("email", { length: 255 }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	ncpdpid: varchar("ncpdpid", { length: 50 }),
	pharmcist_name: varchar("pharmcist_name", { length: 50 }),
	dea: varchar("dea", { length: 50 }),
	pharmacy_legal_name: varchar("pharmacy_legal_name", { length: 100 }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false).notNull(),
},
(table) => {
	return {
		pharmacies_npi_key: unique("pharmacies_npi_key").on(table.npi),
	}
});

export const external_requests_log = pgTable("external_requests_log", {
	external_request_log_id: serial("external_request_log_id").primaryKey().notNull(),
	user_id: integer("user_id").references(() => users.user_id),
	type: varchar("type", { length: 255 }).notNull(),
	detail: text("detail").notNull(),
	timestamp: timestamp("timestamp", { withTimezone: true, mode: 'string' }).notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const mms_patient_invitations = pgTable("mms_patient_invitations", {
	invitation_id: serial("invitation_id").primaryKey().notNull(),
	email: text("email"),
	phone: text("phone"),
	code: text("code").notNull(),
	mms_patient_id: integer("mms_patient_id").notNull().references(() => mms_patients.patient_id),
	accepted: boolean("accepted").default(false),
	expiry: timestamp("expiry", { withTimezone: true, mode: 'string' }),
	remaining_tries: integer("remaining_tries").default(3),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const support_notes = pgTable("support_notes", {
	id: uuid("id").primaryKey().notNull(),
	user_id: integer("user_id").notNull().references(() => users.user_id),
	support_user_id: integer("support_user_id").notNull().references(() => users.user_id),
	order_guid: varchar("order_guid", { length: 50 }).references(() => telehealth_service_order.order_guid),
	general_note: text("general_note").default(''),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
});



export const tenant_auth_provider = pgTable("tenant_auth_provider", {
	auth_id: serial("auth_id").primaryKey().notNull(),
	tenant_id: integer("tenant_id").notNull().references(() => tennant_master.id),
	secret_token: varchar("secret_token", { length: 255 }).notNull(),
	auth_token: varchar("auth_token", { length: 255 }).notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
});

export const diagnoses_values = pgTable("diagnoses_values", {
	value_id: serial("value_id").primaryKey().notNull(),
	summary_id: integer("summary_id").notNull().references(() => user_health_summary.summary_id),
	key: varchar("key", { length: 255 }),
	value: text("value"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const family_history_values = pgTable("family_history_values", {
	value_id: serial("value_id").primaryKey().notNull(),
	summary_id: integer("summary_id").notNull().references(() => user_health_summary.summary_id),
	key: varchar("key", { length: 255 }),
	value: text("value"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const procedures_values = pgTable("procedures_values", {
	value_id: serial("value_id").primaryKey().notNull(),
	summary_id: integer("summary_id").notNull().references(() => user_health_summary.summary_id),
	key: varchar("key", { length: 255 }),
	value: text("value"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const care_plan_values = pgTable("care_plan_values", {
	value_id: serial("value_id").primaryKey().notNull(),
	summary_id: integer("summary_id").notNull().references(() => user_health_summary.summary_id),
	key: varchar("key", { length: 255 }),
	value: text("value"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const results_values = pgTable("results_values", {
	value_id: serial("value_id").primaryKey().notNull(),
	summary_id: integer("summary_id").notNull().references(() => user_health_summary.summary_id),
	key: varchar("key", { length: 255 }),
	value: text("value"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const allergies_values = pgTable("allergies_values", {
	value_id: serial("value_id").primaryKey().notNull(),
	summary_id: integer("summary_id").notNull().references(() => user_health_summary.summary_id),
	key: varchar("key", { length: 255 }),
	value: text("value"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const document_values = pgTable("document_values", {
	value_id: serial("value_id").primaryKey().notNull(),
	summary_id: integer("summary_id").notNull().references(() => user_health_summary.summary_id),
	key: varchar("key", { length: 255 }),
	value: text("value"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const mms_prescription_refills = pgTable("mms_prescription_refills", {
	refill_id: serial("refill_id").primaryKey().notNull(),
	prescription_id: integer("prescription_id").notNull().references(() => mms_prescriptions.prescription_id),
	refill_no: varchar("refill_no", { length: 255 }),
	date_filled: varchar("date_filled", { length: 255 }),
	date_picked: varchar("date_picked", { length: 255 }),
	qty_ordered: numeric("qty_ordered"),
	qty_dispensed: numeric("qty_dispensed"),
	notes: text("notes"),
	delivery_method: text("delivery_method"),
	data: text("data"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	cost_price: numeric("cost_price"),
	disp_fee: numeric("disp_fee"),
	rx_amount: numeric("rx_amount"),
	patient_copay: numeric("patient_copay"),
	billed_amount: numeric("billed_amount"),
	primary_insurance_amount: numeric("primary_insurance_amount"),
	primary_insurance_code: varchar("primary_insurance_code", { length: 255 }),
	secondary_insurance_amount: numeric("secondary_insurance_amount"),
	tertiary_insurance_amount: numeric("tertiary_insurance_amount"),
	status: varchar("status", { length: 255 }),
});

export const vitals_summary_upload_status = pgTable("vitals_summary_upload_status", {
	status_id: serial("status_id").primaryKey().notNull(),
	doctor_id: integer("doctor_id").notNull().references(() => users.user_id),
	patient_id: integer("patient_id").notNull().references(() => users.user_id),
	upload_type: enum_vitals_summary_upload_status_upload_type("upload_type").notNull(),
	upload_status: enum_vitals_summary_upload_status_upload_status("upload_status").default('SUCCESS').notNull(),
	doc_path: text("doc_path"),
	start_date: timestamp("start_date", { withTimezone: true, mode: 'string' }),
	end_date: timestamp("end_date", { withTimezone: true, mode: 'string' }),
	fax_id: integer("fax_id"),
	fax_status: text("fax_status"),
	error: text("error"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const vitals_notification_cycle = pgTable("vitals_notification_cycle", {
	user_id: integer("user_id").primaryKey().notNull().references(() => users.user_id),
	start_date: timestamp("start_date", { withTimezone: true, mode: 'string' }),
	end_date: timestamp("end_date", { withTimezone: true, mode: 'string' }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const visit_summary_upload_status = pgTable("visit_summary_upload_status", {
	status_id: serial("status_id").primaryKey().notNull(),
	order_id: text("order_id").notNull().references(() => orders.order_id),
	user_id: integer("user_id").notNull().references(() => users.user_id),
	upload_type: enum_visit_summary_upload_status_upload_type("upload_type").notNull(),
	upload_status: enum_visit_summary_upload_status_upload_status("upload_status").default('SUCCESS').notNull(),
	error: text("error"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const vitals_patient_monitoring = pgTable("vitals_patient_monitoring", {
	patient_id: integer("patient_id").notNull().references(() => users.user_id),
	buser_id: integer("buser_id").notNull().references(() => users.user_id),
	group_id: serial("group_id").primaryKey().notNull(),
	start_date: timestamp("start_date", { withTimezone: true, mode: 'string' }),
	end_date: timestamp("end_date", { withTimezone: true, mode: 'string' }),
	billed: boolean("billed").default(false),
	billed_date: timestamp("billed_date", { withTimezone: true, mode: 'string' }),
	cost: numeric("cost"),
	currency: text("currency"),
	procedure: text("procedure").default('{"CPT_codes":[{"code":"99454","description":"Device(s) supply with daily recording(s) or programmed alert(s) transmission, each 30 days"}]}'),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const user_forms = pgTable("user_forms", {
	user_form_id: serial("user_form_id").primaryKey().notNull(),
	form_id: integer("form_id").references(() => forms.form_id),
	assigned_by: integer("assigned_by").references(() => users.user_id),
	assigned_to: integer("assigned_to").references(() => users.user_id),
	doctor_id: integer("doctor_id").references(() => users.user_id),
	filled_form_url: text("filled_form_url"),
	data: text("data"),
	status: varchar("status", { length: 255 }),
	score: numeric("score"),
	order_id: integer("order_id").references(() => orders.id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const user_files = pgTable("user_files", {
	user_file_id: serial("user_file_id").primaryKey().notNull(),
	user_id: integer("user_id").references(() => users.user_id),
	created_by: integer("created_by").references(() => users.user_id),
	doctor_id: integer("doctor_id").references(() => users.user_id),
	name: varchar("name", { length: 255 }),
	path: varchar("path", { length: 255 }),
	order_id: integer("order_id").references(() => orders.id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const user_diet = pgTable("user_diet", {
	user_diet_id: serial("user_diet_id").primaryKey().notNull(),
	user_id: integer("user_id").references(() => users.user_id),
	meal_type: enum_user_diet_meal_type("meal_type").notNull(),
	date: timestamp("date", { withTimezone: true, mode: 'string' }),
	image_url: text("image_url"),
	serving: integer("serving"),
	serving_unit: varchar("serving_unit", { length: 255 }),
	food: text("food"),
	order_id: integer("order_id").references(() => orders.id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const telehealth_service_question_answer = pgTable("telehealth_service_question_answer", {
	id: serial("id").primaryKey().notNull(),
	service_order_id: integer("service_order_id").references(() => telehealth_service_order.id),
	question_id: integer("question_id").references(() => telehealth_service_questions.id),
	answer: varchar("answer", { length: 2000 }),
	other_text: varchar("other_text", { length: 2000 }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const faxes = pgTable("faxes", {
	fax_id: serial("fax_id").primaryKey().notNull(),
	fax_sid: text("fax_sid").notNull(),
	fax_number: text("fax_number").notNull(),
	status: text("status").notNull(),
	referral_id: integer("referral_id").references(() => referrals.referral_id),
	detail: text("detail"),
	media_url: text("media_url"),
	sent_by: integer("sent_by").references(() => users.user_id),
	sent_for: integer("sent_for").references(() => users.user_id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const reviews = pgTable("reviews", {
	review_id: serial("review_id").primaryKey().notNull(),
	order_id: text("order_id").references(() => orders.order_id),
	review: text("review").notNull(),
	given_by: integer("given_by").references(() => users.user_id),
	given_to: integer("given_to").references(() => users.user_id),
	rating: integer("rating"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	call_quality: integer("call_quality").default(0),
	is_recommend_provider: boolean("is_recommend_provider"),
	consultation_rating: integer("consultation_rating").default(0),
	user_comment: text("user_comment").default(''),
});

export const referrals = pgTable("referrals", {
	referral_id: serial("referral_id").primaryKey().notNull(),
	referred_by: integer("referred_by").references(() => users.user_id),
	referred_for: integer("referred_for").references(() => users.user_id),
	doctor_id: integer("doctor_id").references(() => users.user_id),
	type: text("type").notNull(),
	name: text("name"),
	path: text("path").notNull(),
	status: enum_referrals_status("status"),
	order_id: integer("order_id").references(() => orders.id),
	detail: text("detail"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	product_id: integer("product_id").references(() => products.product_id),
	prescription_viewed: boolean("prescription_viewed").default(false),
	prescription_processed_by: varchar("prescription_processed_by", { length: 100 }),
	lifefile_order_id: integer("lifefile_order_id"),
	dosespot_prescription_id: integer("dosespot_prescription_id"),
});

export const transcriptions = pgTable("transcriptions", {
	transcription_id: serial("transcription_id").primaryKey().notNull(),
	order_id: text("order_id").references(() => orders.order_id),
	tuser_id: integer("tuser_id").references(() => users.user_id),
	status: text("status"),
	transcript: text("transcript"),
	updated_transcript: text("updated_transcript"),
	audio_stream: text("audio_stream"),
	archive_id: varchar("archive_id", { length: 255 }),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const user_vitals = pgTable("user_vitals", {
	user_vital_id: serial("user_vital_id").primaryKey().notNull(),
	user_id: integer("user_id").references(() => users.user_id),
	value: text("value"),
	metric: varchar("metric", { length: 255 }),
	detail: text("detail"),
	type: varchar("type", { length: 255 }),
	display_name: varchar("display_name", { length: 255 }),
	mode: enum_user_vitals_mode("mode").default('automated'),
	billed: boolean("billed").default(false),
	procedure: text("procedure"),
	abnormal: boolean("abnormal").default(false),
	entity_id: integer("entity_id"),
	bundle_id: varchar("bundle_id", { length: 255 }),
	date: timestamp("date", { withTimezone: true, mode: 'string' }),
	description: text("description"),
	order_id: integer("order_id").references(() => orders.id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	currency: text("currency"),
	cost: numeric("cost"),
	source_platform: varchar("source_platform", { length: 255 }),
},
(table) => {
	return {
		idx_user_vitals_user_id_date: index("idx_user_vitals_user_id_date").using("btree", table.user_id, table.date),
	}
});

export const provider_ratings = pgTable("provider_ratings", {
	id: serial("id").primaryKey().notNull(),
	order_id: integer("order_id").references(() => orders.id),
	provider_id: integer("provider_id").references(() => users.user_id),
	user_id: integer("user_id").references(() => users.user_id),
	rating: integer("rating").default(1),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const tokbox_archive_type = pgTable("tokbox_archive_type", {
	session_id: text("session_id").primaryKey().notNull(),
	archive_id: text("archive_id"),
	type: text("type").notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const schedules = pgTable("schedules", {
	schedule_id: serial("schedule_id").primaryKey().notNull(),
	scheduled_with: integer("scheduled_with").references(() => users.user_id),
	scheduled_by: integer("scheduled_by").references(() => users.user_id),
	start_year: integer("start_year").notNull(),
	start_month: integer("start_month").notNull(),
	start_day: integer("start_day").notNull(),
	end_year: integer("end_year").notNull(),
	end_month: integer("end_month").notNull(),
	end_day: integer("end_day").notNull(),
	start: timestamp("start", { withTimezone: true, mode: 'string' }).notNull(),
	end: timestamp("end", { withTimezone: true, mode: 'string' }).notNull(),
	detail: text("detail"),
	patient_history: text("patient_history"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	order_guid: varchar("order_guid", { length: 50 }).default(NULL::character varying).references(() => telehealth_service_order.order_guid),
	release_medical: boolean("release_medical").default(false),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
});

export const health_summary_metadata = pgTable("health_summary_metadata", {
	metadata_id: serial("metadata_id").primaryKey().notNull(),
	category: text("category").notNull(),
	response_type: text("response_type"),
	template: text("template"),
	array_fields: text("array_fields"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const health_summaries_log = pgTable("health_summaries_log", {
	log_id: serial("log_id").primaryKey().notNull(),
	user_id: integer("user_id").notNull().references(() => users.user_id),
	success: boolean("success").default(false),
	summary_details: text("summary_details"),
	step: varchar("step", { length: 255 }),
	message: text("message"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const conversation_messages = pgTable("conversation_messages", {
	cm_id: serial("cm_id").primaryKey().notNull(),
	message: text("message"),
	user_id: integer("user_id").references(() => users.user_id),
	c_id: integer("c_id").references(() => conversations.c_id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const conversations = pgTable("conversations", {
	c_id: serial("c_id").primaryKey().notNull(),
	user_one: integer("user_one").references(() => users.user_id),
	user_two: integer("user_two").references(() => users.user_id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const prescription_transfer_medications = pgTable("prescription_transfer_medications", {
	medication_id: serial("medication_id").primaryKey().notNull(),
	request_id: integer("request_id").references(() => prescription_transfer_request.request_id),
	name: text("name").notNull(),
	is_fulfilled: boolean("is_fulfilled").default(false),
	hg_rx_id: integer("hg_rx_id"),
	status: enum_prescription_transfer_medications_status("status").default('pending'),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const prescription_transfer_request = pgTable("prescription_transfer_request", {
	request_id: serial("request_id").primaryKey().notNull(),
	user_id: integer("user_id").references(() => users.user_id),
	zipcode: text("zipcode").notNull(),
	pharmacy_name: text("pharmacy_name").notNull(),
	pharmacy_address: text("pharmacy_address").notNull(),
	first_name: text("first_name").notNull(),
	last_name: text("last_name").notNull(),
	transfer_all: boolean("transfer_all").default(false).notNull(),
	phone: text("phone").notNull(),
	dob: text("dob").notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const refill_request = pgTable("refill_request", {
	id: serial("id").primaryKey().notNull(),
	service_order_id: integer("service_order_id").notNull().references(() => telehealth_service_order.id),
	drug_details: varchar("drug_details", { length: 5000 }).notNull(),
	prescription_images: varchar("prescription_images", { length: 5000 }).notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
},
(table) => {
	return {
		drug_details: index("refill_request_drug_details").using("btree", table.drug_details),
	}
});

export const pharmacy_state_service_mapping = pgTable("pharmacy_state_service_mapping", {
	id: serial("id").primaryKey().notNull(),
	service_id: integer("service_id").references(() => telehealth_services.id),
	state_id: integer("state_id").references(() => states.state_id),
	pharmacy_id: integer("pharmacy_id").references(() => pharmacies.pharmacy_id),
	status: boolean("status").default(true).notNull(),
});

export const drugs_category = pgTable("drugs_category", {
	category_id: serial("category_id").primaryKey().notNull(),
	category_name: varchar("category_name", { length: 200 }).notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
});

export const states = pgTable("states", {
	state_id: serial("state_id").primaryKey().notNull(),
	name: varchar("name", { length: 50 }).notNull(),
	abbreviation: varchar("abbreviation", { length: 2 }).notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
});

export const telehealth_service_state_mapping = pgTable("telehealth_service_state_mapping", {
	id: serial("id").primaryKey().notNull(),
	state_id: integer("state_id").notNull().references(() => states.state_id),
	service_id: integer("service_id").references(() => telehealth_services.id),
	status: boolean("status").default(true),
});

export const webhooks_log = pgTable("webhooks_log", {
	webhooks_log_id: serial("webhooks_log_id").primaryKey().notNull(),
	body: varchar("body", { length: 5000 }),
	url: varchar("url", { length: 255 }).notNull(),
	response_status: varchar("response_status", { length: 255 }),
	type: varchar("type", { length: 255 }).notNull(),
	case_id: varchar("case_id", { length: 255 }).notNull(),
	status: enum_webhooks_log_status("status"),
	failed_message: text("failed_message"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	deleted: boolean("deleted").default(false),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
});

export const user_practice_groups = pgTable("user_practice_groups", {
	user_id: integer("user_id").notNull().references(() => users.user_id),
	practice_group_id: integer("practice_group_id").notNull().references(() => practice_groups.practice_group_id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
},
(table) => {
	return {
		user_practice_groups_pkey: primaryKey({ columns: [table.user_id, table.practice_group_id], name: "user_practice_groups_pkey"}),
	}
});

export const schedule_translation = pgTable("schedule_translation", {
	schedule_id: integer("schedule_id").notNull().references(() => schedules.schedule_id),
	language_code: varchar("language_code", { length: 255 }).notNull(),
	patient_history: text("patient_history"),
	detail: text("detail"),
},
(table) => {
	return {
		schedule_translation_pkey: primaryKey({ columns: [table.schedule_id, table.language_code], name: "schedule_translation_pkey"}),
	}
});

export const request_translation = pgTable("request_translation", {
	request_id: integer("request_id").notNull().references(() => requests.request_id),
	language_code: varchar("language_code", { length: 255 }).notNull(),
	message: text("message"),
	patient_history: text("patient_history"),
	detail: text("detail"),
},
(table) => {
	return {
		request_translation_pkey: primaryKey({ columns: [table.request_id, table.language_code], name: "request_translation_pkey"}),
	}
});

export const users_translation = pgTable("users_translation", {
	user_id: integer("user_id").notNull().references(() => users.user_id),
	language_code: varchar("language_code", { length: 255 }).notNull(),
	first_name: text("first_name"),
	last_name: text("last_name"),
	history: text("history"),
	questionnaire: text("questionnaire"),
},
(table) => {
	return {
		users_translation_pkey: primaryKey({ columns: [table.user_id, table.language_code], name: "users_translation_pkey"}),
	}
});

export const user_viewers = pgTable("user_viewers", {
	user_id: integer("user_id").notNull().references(() => users.user_id),
	viewer_id: integer("viewer_id").notNull().references(() => users.user_id),
	user_favorite: boolean("user_favorite").default(false),
	viewer_favorite: boolean("viewer_favorite").default(false),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
},
(table) => {
	return {
		user_viewers_pkey: primaryKey({ columns: [table.user_id, table.viewer_id], name: "user_viewers_pkey"}),
	}
});

export const user_associations = pgTable("user_associations", {
	user_id: integer("user_id").notNull().references(() => users.user_id),
	buser_id: integer("buser_id").notNull().references(() => users.user_id),
	user_favorite: boolean("user_favorite").default(false),
	buser_favorite: boolean("buser_favorite").default(false),
	ma_manage_orders: boolean("ma_manage_orders").default(false),
	is_notify_on_capture: boolean("is_notify_on_capture").default(false),
	is_rpm_enabled: boolean("is_rpm_enabled").default(false),
	is_customized_vitals_thresholds: boolean("is_customized_vitals_thresholds").default(false),
	vitals_thresholds: text("vitals_thresholds"),
	vitals_cron: text("vitals_cron"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
},
(table) => {
	return {
		user_associations_pkey: primaryKey({ columns: [table.user_id, table.buser_id], name: "user_associations_pkey"}),
	}
});