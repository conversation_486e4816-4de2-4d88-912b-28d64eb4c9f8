import {
  boolean,
  pgTable,
  serial,
  text,
  timestamp,
  uuid
} from 'drizzle-orm/pg-core';
import { chat_rooms } from './chat_rooms';
import { relations } from 'drizzle-orm';
import { users } from './users';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

export const chat_files = pgTable('chat_files', {
  file_id: serial('file_id').primaryKey(),
  user_id: serial('user_id')
    .references(() => users.user_id)
    .notNull(),
  room_id: serial('room_id')
    .references(() => chat_rooms.id)
    .notNull(), 
  name: text('name'),
  path: text('path'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow()
});

export const chatFilesRelation = relations(
  chat_files,
  ({ one, many }) => ({
    users: one(users, {
      fields: [chat_files.user_id],
      references: [users.user_id]
    }),
    chat_rooms: one(chat_rooms, {
      fields: [chat_files.room_id],
      references: [chat_rooms.id]
    })
  })
);


export const insertChatFilesSchema = createInsertSchema(chat_files);
export const selectChatFilesSchema = createSelectSchema(chat_files);