import { boolean, pgTable, serial, text, timestamp } from 'drizzle-orm/pg-core';
import { chat_rooms } from './chat_rooms';
import { relations, sql } from 'drizzle-orm';
import { users } from './users';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { chat_files } from './chat_files';

export const chat_messages = pgTable('chat_messages', {
  id: serial('id').primaryKey(),
  message: text('message'),
  file_id: serial('file_id'),
  sender_id: serial('sender_id')
    .references(() => users.user_id)
    .notNull(),
  room_id: serial('room_id')
    .references(() => chat_rooms.id)
    .notNull(),
  read_by: text('read_by')
    .array()
    .notNull()
    .default(sql`ARRAY[]::text[]`),
  deleted: boolean('deleted').notNull().default(false),
  deletedAt: timestamp('deleted_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow()
});

export const chatMessageRelation = relations(
  chat_messages,
  ({ one, many }) => ({
    users: one(users, {
      fields: [chat_messages.sender_id],
      references: [users.user_id]
    }),
    chat_files: one(chat_files, {
      fields: [chat_messages.file_id],
      references: [chat_files.file_id]
    }),
    chat_rooms: one(chat_rooms, {
      fields: [chat_messages.room_id],
      references: [chat_rooms.id]
    })
  })
);

export const insertChatMessagesSchema = createInsertSchema(chat_messages);
export const selectChatMessagesSchema = createSelectSchema(chat_messages);
