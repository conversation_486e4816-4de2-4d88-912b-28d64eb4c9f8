import { relations } from 'drizzle-orm';
import { boolean, pgTable, serial, timestamp } from 'drizzle-orm/pg-core';
import { chat_rooms } from './chat_rooms';
import { users } from './users';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

export const chat_room_members = pgTable('chat_room_members', {
  id: serial('id').primaryKey(),
  user_id: serial('user_id')
    .references(() => users.user_id)
    .notNull(),
  room_id: serial('room_id')
    .references(() => chat_rooms.id)
    .notNull(),
  deleted: boolean('deleted').notNull().default(false),
  deletedAt: timestamp('deleted_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow()
});

export const chatRoomMemberRelations = relations(chat_room_members, ({ one }) => ({
  user: one(users, {
    fields: [chat_room_members.user_id],
    references: [users.user_id],
  }),
  chat_rooms: one(chat_rooms, {
    fields: [chat_room_members.room_id],
    references: [chat_rooms.id],
  }),
}));

export const insertChatRoomMembersSchema = createInsertSchema(chat_room_members);
export const selectChatRoomMembersSchema = createSelectSchema(chat_room_members);
