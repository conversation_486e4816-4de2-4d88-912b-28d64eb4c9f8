import {
  boolean,
  pgTable,
  serial,
  text,
  timestamp,
  uuid
} from 'drizzle-orm/pg-core';
import { chat_room_members } from './chat_room_members';
import { relations } from 'drizzle-orm';
import { chat_messages } from './chat_messages';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

export const chat_rooms = pgTable('chat_rooms', {
  id: serial('id').primaryKey(),
  room_name: text('room_name').notNull(),
  room_identifier: uuid('room_identifier').defaultRandom(),
  service_key: text('service_key'),
  description: text('description'),
  last_message_at: timestamp('last_message_at'),
  last_message: text('last_message'),
  active: boolean('active').notNull().default(true),
  deleted: boolean('deleted').notNull().default(false),
  deletedAt: timestamp('deleted_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow()
});

export const chatRoomRelations = relations(chat_rooms, ({ many }) => ({
  chat_room_members: many(chat_room_members), // Renamed for consistency
  chat_messages: many(chat_messages)
}));

export const insertChatRoomSchema = createInsertSchema(chat_rooms);
export const selectChatRoomSchema = createSelectSchema(chat_rooms);
