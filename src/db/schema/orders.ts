import { AnyPgColumn, boolean, integer, numeric, pgEnum, pgTable, serial, text, timestamp, unique, varchar } from 'drizzle-orm/pg-core'
import { users } from './users';
import { telehealth_service_order } from './telehealth_service_orders';

export const enum_orders_category = pgEnum("enum_orders_category", ['CCM', 'RPM', 'BHI'])
export const enum_orders_status = pgEnum("enum_orders_status", ['STARTED', 'INPROGRESS', 'ENDED', 'NOANSWER', 'REJECTED', 'QUEUED', 'RINGING', 'CANCELLED', 'COMPLETED', 'BUSY', 'FAILED'])
export const enum_orders_type = pgEnum("enum_orders_type", ['audio', 'video', 'one-way', 'online', 'voip'])

export const orders = pgTable("orders", {
	id: serial("id").primaryKey().notNull(),
	order_id: text("order_id").notNull(),
	// schedule_id: integer("schedule_id").references(() => schedules.schedule_id),
	start_time: timestamp("start_time", { withTimezone: true, mode: 'string' }),
	end_time: timestamp("end_time", { withTimezone: true, mode: 'string' }),
	caller_id: integer("caller_id").references(() => users.user_id),
	callee_id: integer("callee_id").references(() => users.user_id),
	doctor_id: integer("doctor_id").references(() => users.user_id),
	status: enum_orders_status("status"),
	type: enum_orders_type("type"),
	category: enum_orders_category("category"),
	conversation_mode: text("conversation_mode"),
	cost: numeric("cost"),
	billed: boolean("billed").default(false),
	caller_location: varchar("caller_location", { length: 255 }),
	callee_location: varchar("callee_location", { length: 255 }),
	instructions: text("instructions"),
	diagnosis: text("diagnosis"),
	procedure: text("procedure"),
	visit_summary: text("visit_summary"),
	regenerate_visit_summary: boolean("regenerate_visit_summary").default(false),
	regenerate_ccd_file: boolean("regenerate_ccd_file").default(false),
	is_virtual_room: boolean("is_virtual_room").default(false),
	hide_visit_details: boolean("hide_visit_details").default(false),
	audio_stream_screenshot: text("audio_stream_screenshot"),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	currency: text("currency"),
	order_guid: varchar("order_guid", { length: 50 }).references((): AnyPgColumn => telehealth_service_order.order_guid),
	duration: varchar("duration", { length: 50 }),
},
(table) => {
	return {
		orders_order_id_key: unique("orders_order_id_key").on(table.order_id),
	}
});
