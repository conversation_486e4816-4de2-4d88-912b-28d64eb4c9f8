import { AnyPgColumn, boolean, integer, pgEnum, pgTable, serial, timestamp, unique, varchar } from 'drizzle-orm/pg-core';
import { users } from './users';
import { telehealth_services } from './telehealth_services';
import { relations } from 'drizzle-orm';
import { orders } from './orders';
export const enum_telehealth_service_order_claim_type = pgEnum("enum_telehealth_service_order_claim_type", ['AUTO', 'WORK'])
export const enum_telehealth_service_order_service_type = pgEnum("enum_telehealth_service_order_service_type", ['SYNC', 'ASYNC'])
export const enum_telehealth_service_order_status = pgEnum("enum_telehealth_service_order_status", ['pending', 'accept', 'completed', 'errored', 'cancelled', 'patient_verification_pending', 'archive', 'cancelled_by_provider', 'LabRequested', 'LabReceived', 'schedule_pending', 'lab_approval_pending', 'lab_results_approved', 'lab_results_denied', 'clinical_denial'])
export const enum_telehealth_service_order_visit_type = pgEnum("enum_telehealth_service_order_visit_type", ['IN_PERSON', 'ONLINE'])



export const telehealth_service_order = pgTable("telehealth_service_order", {
	id: serial("id").primaryKey().notNull(),
	service_id: integer("service_id").references(() => telehealth_services.id),
	answer_given_by: integer("answer_given_by").references(() => users.user_id),
	provider_id: integer("provider_id").references(() => users.user_id),
	order_id: integer("order_id").references((): AnyPgColumn => orders.id),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	order_guid: varchar("order_guid", { length: 50 }),
	status: enum_telehealth_service_order_status("status"),
	prescription_delivery: boolean("prescription_delivery").default(false),
	ravkoo_prescription_option: boolean("ravkoo_prescription_option").default(false),
	pharmacy_name: varchar("pharmacy_name", { length: 100 }).default(''),
	pharmacy_phone: varchar("pharmacy_phone", { length: 20 }).default(''),
	pharmacy_address: varchar("pharmacy_address", { length: 255 }).default(''),
	service_type: enum_telehealth_service_order_service_type("service_type").default('SYNC'),
	release_medical: boolean("release_medical").default(false),
	release_medical_at: timestamp("release_medical_at", { withTimezone: true, mode: 'string' }),
	is_cancelled_by_provider: boolean("is_cancelled_by_provider").default(false),
	cancelled_at: timestamp("cancelled_at", { withTimezone: true, mode: 'string' }),
	pharmacy_city: varchar("pharmacy_city", { length: 20 }),
	pharmacy_state: varchar("pharmacy_state", { length: 20 }),
	pharmacy_zip: varchar("pharmacy_zip", { length: 20 }),
	pharmacy_fax: varchar("pharmacy_fax", { length: 20 }),
	pharmacy_preference: integer("pharmacy_preference").default(0).notNull(),
	claim_type: enum_telehealth_service_order_claim_type("claim_type"),
	claim_id: varchar("claim_id", { length: 255 }),
	payor_name: varchar("payor_name", { length: 255 }),
	injury_date: timestamp("injury_date", { withTimezone: true, mode: 'string' }),
	current_medicines: varchar("current_medicines", { length: 255 }),
	allergies_to_medicines: varchar("allergies_to_medicines", { length: 255 }),
	other_allergies: varchar("other_allergies", { length: 255 }),
	doctor_notes: varchar("doctor_notes", { length: 255 }),
	abnormal_findings: varchar("abnormal_findings", { length: 255 }),
	terms_and_conditions_accepted: boolean("terms_and_conditions_accepted").default(false),
	cancellation_reason: varchar("cancellation_reason", { length: 255 }),
	is_refill_request: boolean("is_refill_request").default(false),
	external_order_id: varchar("external_order_id", { length: 50 }),
	follow_up: integer("follow_up"),
	visit_type: enum_telehealth_service_order_visit_type("visit_type"),
	completion_reason: varchar("completion_reason", { length: 255 }),
	session_type: varchar("session_type", { length: 255 }),
	schedule_type: varchar("schedule_type", { length: 255 }),
	affiliateid: varchar("affiliateid", { length: 255 }),
	client_name: varchar("client_name", { length: 255 }),
	dosespot_pharmacy_id: varchar("dosespot_pharmacy_id", { length: 200 }),
	pharmacy_ncpdp_id: varchar("pharmacy_ncpdp_id", { length: 255 }),
	erx_prescription_visitied_at: timestamp("erx_prescription_visitied_at", { withTimezone: true, mode: 'string' }),
},
(table) => {
	return {
		telehealth_service_order_order_guid_key: unique("telehealth_service_order_order_guid_key").on(table.order_guid),
	}
});

export const telehealth_service_orderRelations = relations(telehealth_service_order, ({one, many}) => ({
	// consult_notes: many(consult_notes),
	// requests: many(requests),
	// orders: many(orders, {
	// 	relationName: "orders_order_guid_telehealth_service_order_order_guid"
	// }),
	user_answer_given_by: one(users, {
		fields: [telehealth_service_order.answer_given_by],
		references: [users.user_id],
		relationName: "telehealth_service_order_answer_given_by_users_user_id"
	}),
	order: one(orders, {
		fields: [telehealth_service_order.order_id],
		references: [orders.id],
		relationName: "telehealth_service_order_order_id_orders_id"
	}),
	user_provider_id: one(users, {
		fields: [telehealth_service_order.provider_id],
		references: [users.user_id],
		relationName: "telehealth_service_order_provider_id_users_user_id"
	}),
	telehealth_service: one(telehealth_services, {
		fields: [telehealth_service_order.service_id],
		references: [telehealth_services.id]
	}),
	// consult_update_details_histories: many(consult_update_details_history),
	// follow_up_reminders: many(follow_up_reminder),
	// consult_order_files: many(consult_order_files),
	// jobs: many(jobs),
	// telehealth_service_question_answer_dumps: many(telehealth_service_question_answer_dump),
	// consult_reassign_histories: many(consult_reassign_history),
	// support_notes: many(support_notes),
	// telehealth_service_question_answers: many(telehealth_service_question_answer),
	// schedules: many(schedules),
	// refill_requests: many(refill_request),
}));