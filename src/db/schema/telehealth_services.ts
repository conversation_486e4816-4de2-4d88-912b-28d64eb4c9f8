import { relations } from 'drizzle-orm';
import { boolean, integer, pgEnum, pgTable, serial, text, timestamp, unique, varchar } from 'drizzle-orm/pg-core';
import { telehealth_service_order } from './telehealth_service_orders';

export const enum_telehealth_services_service_mode = pgEnum("enum_telehealth_services_service_mode", ['SYNC', 'ASYNC', 'BOTH_SYNC_ASYNC'])
export const enum_telehealth_services_service_type = pgEnum("enum_telehealth_services_service_type", ['BUSER', 'AUSER', 'medical_assistant', 'pharmacist', 'DIETICIAN_NUTRITION', 'MENTAL_HEALTH', 'WEIGHT_LOSS_MANAGEMENT', 'DIABETES_PREVENTION'])


export const telehealth_services = pgTable("telehealth_services", {
	id: serial("id").primaryKey().notNull(),
	service_name: varchar("service_name", { length: 200 }).notNull(),
	description: varchar("description", { length: 500 }).notNull(),
	created_at: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updated_at: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	service_type: enum_telehealth_services_service_type("service_type").default('BUSER'),
	display_order: integer("display_order").default(1),
	service_mode: enum_telehealth_services_service_mode("service_mode").default('BOTH_SYNC_ASYNC'),
	deleted: boolean("deleted").default(false),
	deleted_at: timestamp("deleted_at", { withTimezone: true, mode: 'string' }),
	amount: integer("amount"),
	tenant_id: integer("tenant_id"),
	paypal_plan_id: text("paypal_plan_id"),
	title: varchar("title", { length: 255 }),
	subtitle: varchar("subtitle", { length: 255 }),
	service_key: varchar("service_key", { length: 255 }),
	session_type: varchar("session_type", { length: 255 }),
	fields_options: varchar("fields_options", { length: 255 }),
	on_complete_script: varchar("on_complete_script", { length: 2000 }),
	on_follow_up_script: varchar("on_follow_up_script", { length: 2000 }),
	access_labs_test_code: varchar("access_labs_test_code", { length: 2000 }),
	on_create_script: varchar("on_create_script", { length: 2000 }),
	on_lab_result_received_script: varchar("on_lab_result_received_script", { length: 2000 }),
	on_update_schedule_script: varchar("on_update_schedule_script", { length: 2000 }),
	disclaimer: text("disclaimer"),
	service_details: text("service_details"),
	is_video_call: boolean("is_video_call").default(true).notNull(),
	is_audio_call: boolean("is_audio_call").default(true).notNull(),
	display_questionnaire: boolean("display_questionnaire").default(false).notNull(),
	display_service_name: text("display_service_name"),
	erx_drug_key: varchar("erx_drug_key", { length: 255 }),
},
(table) => {
	return {
		telehealth_services_service_name_key: unique("telehealth_services_service_name_key").on(table.service_name),
	}
});


export const telehealth_servicesRelations = relations(telehealth_services, ({many}) => ({
	// promo_codes: many(promo_codes),
	telehealth_service_orders: many(telehealth_service_order),
	// medicine_service_pharmacy_mappings: many(medicine_service_pharmacy_mapping),
	// telehealth_service_questions: many(telehealth_service_questions),
	// telehealth_service_provider_mappings: many(telehealth_service_provider_mapping),
	// sms_templates: many(sms_template),
	// email_templates: many(email_template),
	// pharmacy_state_service_mappings: many(pharmacy_state_service_mapping),
	// telehealth_service_state_mappings: many(telehealth_service_state_mapping),
}));