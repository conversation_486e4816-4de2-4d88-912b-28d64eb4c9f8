import { boolean, pgTable, serial, text, timestamp } from 'drizzle-orm/pg-core';
import { users } from './users';
import { relations } from 'drizzle-orm';

export const tennant_master = pgTable('tennant_master', {
  id: serial('id').primaryKey(),
  tennant_name: text('tennant_name'),
  support_email: text('support_email'),
  filter_display: boolean('filter_display').notNull().default(false),
  parent_id: serial('parent_id'),
  access_lab_client_number: text('access_lab_client_number'),
  mailchip_from_email: text('mailchip_from_email'),
  mailchip_from_email_name: text('mailchip_from_email_name'),
  paypal_client_id: text('paypal_client_id'),
  paypal_client_secret: text('paypal_client_secret'),
  stripe_client_id: text('stripe_client_id'),
  stripe_client_secret: text('stripe_client_secret'),
  preferred_payment_gateway: text('preferred_payment_gateway'),
  twilio_account_sid: text('twilio_account_sid'),
  twilio_auth_token: text('twilio_auth_token'),
  twilio_phone_number: text('twilio_phone_number'),
  tennant_display_name: text('tennant_display_name'),
  dosespot_client_id: text('dosespot_client_id'),
  dosespot_client_secret: text('dosespot_client_secret'),

  deleted: boolean('deleted').notNull().default(false),
  deletedAt: timestamp('deleted_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow()
});


export const tennantMasterRelation = relations(
  tennant_master,
  ({ one, many }) => ({
    users: one(users,{
      fields: [tennant_master.id],
      references: [users.tennant_id]
    }),
    parent_id: one(tennant_master,{
      fields: [tennant_master.id],
      references: [tennant_master.parent_id]
    })
  })
)