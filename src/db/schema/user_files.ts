import {
  boolean,
  pgTable,
  serial,
  text,
  timestamp,
  uuid
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

export const user_files = pgTable('user_files', {
  user_file_id: serial('user_file_id').primaryKey(),
  user_id: serial('user_id')
    .references(() => users.user_id)
    .notNull(),
  created_by: serial('created_by').references(() => users.user_id).notNull(),  
  doctor_id: serial('doctor_id').references(() => users.user_id).notNull(),  
  name: text('name'),
  path: text('path'),
  order_id: serial('order_id'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow()
});

export const userFilesRelation = relations(
  user_files,
  ({ one, many }) => ({
    users: one(users, {
      fields: [user_files.user_id],
      references: [users.user_id]
    }),
    created: one(users, {
      fields: [user_files.created_by],
      references: [users.user_id]
    }),
    doctors: one(users, {
      fields: [user_files.doctor_id],
      references: [users.user_id]
    }),
   
  })
);


export const insertUserFilesSchema = createInsertSchema(user_files);
export const selectUserFilesSchema = createSelectSchema(user_files);