import { relations } from 'drizzle-orm';
import {
  pgEnum,
  pgTable,
  serial,
  text,
  timestamp,
  uuid,
  boolean,
  json,
} from 'drizzle-orm/pg-core';
import { tennant_master } from './tennant_master';
import { chat_rooms } from './chat_rooms';
import { chat_messages } from './chat_messages';

export const userRoleEnum = pgEnum('user_role', [
  'USER',
  'BUSER',
  'AUSER',
  'medical_assistant',
  'viewer',
  'support_user',
  'pharmacist',
  'PHARMACY',
  'GROUP_ADMIN',
  'SUPPORT_ADMIN'
]);
export const userStatusEnum = pgEnum('user_status', [
  'AVAILABLE',
  'BUSY',
  'AWAY',
  'OFFLINE',
  'ACTIVATION_PENDING',
  'ONBOARDING_PENDING',
  'PROFILE_INCOMPLETE'
]);

export const userGenderEnum = pgEnum('user_gender', [
  'male',
  'female',
  'others',
  'transgender-male',
  'transgender-female'
]);

export const userSubRoleEnum = pgEnum('user_sub_role', [
  'USER',
  'DIETICIAN_NUTRITION',
  'MENTAL_HEALTH',
  'WEIGHT_LOSS_MANAGEMENT',
  'DIABETES_PREVENTION'
]);

export const users = pgTable('users', {
  user_id: serial('user_id').primaryKey(),
  user_guid: uuid('user_guid').defaultRandom(),
  password: text('password'),
  role: userRoleEnum('role').notNull().default('USER'),
  status: userStatusEnum('status').notNull().default('OFFLINE'),
  install_type: text('install_type'),
  first_name: text('first_name'),
  last_name: text('last_name'),
  email: text('email'),
  email_2: text('email_2'),
  phone: text('phone'),
  secondary_phone: text('secondary_phone'),
  gender: userGenderEnum('gender'),
  user_avatar: text('user_avatar'),
  dob: text('dob'),
  otp: text('otp'),
  last_active: timestamp('last_active'),
  rxPersonId: text('rxPersonId'),
  rxStatus: text('rxStatus'),
  dosespot_api_response: text('dosespot_api_response'),
  sub_role: userSubRoleEnum('sub_role').notNull().default('USER'),
  tennant_id: serial('tennant_id'),
  is_tennant_owner: boolean('is_tennant_owner').notNull().default(false),
  app_timezone: json('app_timezone'),
  tenant_access: text('tenant_access'),
  deleted: boolean('deleted').notNull().default(false),
  // deletedAt: timestamp('deleted_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow()
});


export const userRelation = relations(users, ({ one, many }) => ({
  tennant_master: one(tennant_master, {
    fields: [users.tennant_id],
    references: [tennant_master.id]
  }),
  chat_rooms: many(chat_rooms),
  chat_messages: many(chat_messages)
}))


export type SelectUser = typeof users.$inferSelect;
export type InsertUser = typeof users.$inferInsert;