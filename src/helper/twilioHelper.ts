import config from "../config";
var twilio = require('twilio');
var twilioClient = new twilio(config.TWILIO_ACCOUNT_SID, config.TWILIO_AUTHTOKEN);
export const sendSMS = function (phoneNumber: any, messageText: any) {
    twilioClient.messages.create({
        to: phoneNumber,
        from: config.TWILIO_PHONE_NUMBER,
        body: messageText,
        messagingServiceSid: config.TWILIO_MESSAGE_SID,
    }, function (err: null) {
        if(err != null) {
            console.error("********************************************************** sendSMS()");
            console.error(err);                                          
        }
    });
}
