import dotnev from 'dotenv';
dotnev.config();
import { createServer } from 'http';

import app from './app';
import config from './config';
import registerSocketServer from './utils/websocket';
import { Part } from 'aws-sdk/clients/s3';
import { SelectUser, users } from './db/schema';

const server = createServer(app);
registerSocketServer(server);

// new SocketServer(config.SOCKET_PORT as number, {
//   host: config.RADIS_SERVER,
//   port: config.RADIS_PORT as number
// });

server.listen(config.SERVER_PORT, () => {
  console.log(`Server is running on port ${config.SERVER_PORT}`);
});


type User = {
  user_id: number;
  first_name: unknown;
  last_name: unknown;
  email: string | null;
  role:
    | 'USER'
    | 'BUSER'
    | 'AUSER'
    | 'medical_assistant'
    | 'viewer'
    | 'support_user'
    | 'pharmacist'
    | 'PHARMACY'
    | 'GROUP_ADMIN'
    | 'SUPPORT_ADMIN';
}

declare global {
  namespace Express {
    interface Request {
      user: User;
    }
  }
}
