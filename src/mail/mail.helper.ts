import Mailchimp, {
  MessagesSendTemplateRequest,
  RecipientMergeVar,
  TemplateContent
} from '@mailchimp/mailchimp_transactional';
import config from '../config';

const MailChimpCLient = Mailchimp(config.MAILCHIMP_API_KEY as string);

export const sendLoginOtpEmail = async (
  toEmail: string,
  fName: string,
  otp: string
) => {
  const template_name = 'Chat Functionality | OTP Verify Account';

  const subject = 'Chat App Login OTP';
  const template_content: TemplateContent[] = [
    { name: 'FIRST_NAME', content: fName },
    { name: 'OTP_CODE', content: otp }
  ];

  const merge_vars: RecipientMergeVar[] = [
    {
      rcpt: toEmail,
      vars: [
        { name: 'FIRST_NAME', content: fName },
        { name: 'OTP_CODE', content: otp }
      ]
    }
  ];
  await sendEmail(
    toEmail,
    subject,
    template_name,
    template_content,
    merge_vars
  );
};

export const sendChatLinkEmail = async (
  toEmail: string,
  fName: string,
  chatLink: string
) => {
  const template_name = 'Chat Functionality | Link To Chat';

  const subject = 'Chat App | Link To Chat';
  const template_content: TemplateContent[] = [
    { name: 'FIRST_NAME', content: fName },
    { name: 'CHAT_LINK', content: chatLink }
  ];

  const merge_vars: RecipientMergeVar[] = [
    {
      rcpt: toEmail,
      vars: [
        { name: 'FIRST_NAME', content: fName },
        { name: 'CHAT_LINK', content: chatLink }
      ]
    }
  ];
  await sendEmail(
    toEmail,
    subject,
    template_name,
    template_content,
    merge_vars
  );
};

export const sendEmail = async (
  toEmail: string,
  subject: string,
  template_name: string,
  template_content: TemplateContent[],
  merge_vars?: RecipientMergeVar[]
) => {
  const sendMessage: MessagesSendTemplateRequest = {
    template_name,
    template_content,
    message: {
      to: [
        {
          email: toEmail,
          type: 'to'
        }
      ],
      from_email: config.MAILCHIMP_SENDER_EMAIL,
      from_name: config.MAILCHIMP_FROM_NAME,
      subject: subject,
      merge_vars,
      auto_html: true,
      inline_css: true,
      view_content_link: true
    }
  };

  const user =await MailChimpCLient.messages.sendTemplate(sendMessage);
  console.log(user);
};
