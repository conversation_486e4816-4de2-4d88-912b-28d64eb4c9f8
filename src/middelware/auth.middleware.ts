import { Request, Response, NextFunction } from 'express';
import jwt, { JwtPayload } from 'jsonwebtoken';
import moment from 'moment-timezone';
import { users } from '../db/schema';
import { db } from '../db';
import { eq } from 'drizzle-orm';
import config from '../config';
import ApiError from '../helper/ApiError';
import { decryptField } from '../db/custom/pgcrypto';

export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const bearerHeader = req.headers.authorization;
    if (typeof bearerHeader !== 'undefined') {
      const bearerToken = bearerHeader.split(' ')[1];

      const tokenInfo = jwt.verify(bearerToken, config.SECRET!) as JwtPayload;


      if (tokenInfo.exp! < moment().unix()) {
        throw new ApiError(401, 'Token was expired!');
      }

      const userInfo = await db
        .select({
          user_id: users.user_id,
          first_name: await decrypt<PERSON><PERSON>(users.first_name),
          last_name: await decrypt<PERSON><PERSON>(users.last_name),
          email: users.email,
          role: users.role,
        })
        .from(users)
        .where(eq(users.user_id, tokenInfo?.user_id));

      if (!userInfo || !userInfo[0]) {
        throw new ApiError(401, 'Invalid token');
      }


      req.user = userInfo[0];
      next();
    } else {
      throw new ApiError(401, 'Unauthorized');
    }
  } catch (error:any) {
    next(error);
  }
};
