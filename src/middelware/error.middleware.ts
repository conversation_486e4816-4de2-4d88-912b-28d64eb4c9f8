import { Request, Response, NextFunction } from 'express';

import winstonLogger from '../utils/logger/winston';
import ApiError from '../helper/ApiError';
import ApiResponse from '../helper/ApiResponse';

const errorHandler = (
  err: any,
  _: Request,
  res: Response,
  __: NextFunction
) => {
  let error = err;
  if (!error) {
    // if not
    // create a new ApiError instance to keep the consistency
    // assign an appropriate status code
    const statusCode = error.statusCode ? 400 : 500;

    // set a message from native Error instance or a custom one
    const message = error.message || 'Something went wrong';
    console.log('statusCode', statusCode);
    console.log('message', message);

    error = new ApiError(statusCode, message, error?.errors || [], err.stack);
  }
  const response = {
    ...error,
    message: error.message,
    stack: error.stack,
    ...(process.env.NODE_ENV === 'development' ? { stack: error.stack } : {}) // Error stack traces should be visible in development for debugging
  };

  if (error.name === 'JsonWebTokenError') {
    error.statusCode = 401;
    error.message = 'Unauthorized';
  }

  winstonLogger.error(`${error.message}`);

  const statusCode = error.statusCode || 500;

  // Send error response
  return res
    .status(statusCode)
    .json(
      new ApiResponse(
        statusCode,
        response,
        error.message ?? 'Something went wrong'
      )
    );
};

export default errorHandler;
