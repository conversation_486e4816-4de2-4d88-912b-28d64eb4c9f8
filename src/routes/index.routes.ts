import { Router } from "express";

import FileRoutes from "../components/file/file.routes";
import ChatRoutes from "../components/chat/chat.routes";
import AuthRoutes from "../components/auth/auth.routes";
import RoomRoutes from "../components/room/room.routes";
import NotificationRoutes from "../components/notification/notification.routes";
const router = Router();


router.use("/files", FileRoutes);
router.use("/chat", ChatRoutes);
router.use("/auth", AuthRoutes);
router.use("/room", RoomRoutes);
router.use("/notification", NotificationRoutes);


export default router;
