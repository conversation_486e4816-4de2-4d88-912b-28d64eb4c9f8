import { db } from '../db';
import { chat_messages, chat_rooms, users } from '../db/schema';
import { and, asc, eq, sql } from 'drizzle-orm';
import { chat_files } from '../db/schema/chat_files';
import { decryptField, encryptField } from '../db/custom/pgcrypto';
export const sendChatMessage = async (
  message: string,
  sender_id: number,
  file_id: number
) => {
  await db
    .insert(chat_messages)
    .values({
      message: message,
      sender_id: sender_id,
      file_id: file_id
    })
    .returning();
};
export const sendChatMessageRoom = async (
  message: string,
  sender_id: number,
  room_id: number,
  file_id: number
) => {
  try {
    await db
      .insert(chat_messages)
      .values({
        message: await encryptField(message),
        sender_id: sender_id,
        room_id: room_id,
        file_id: file_id
      })
      .returning();

    await db
      .update(chat_rooms)
      .set({ last_message_at: sql`now()`, last_message: message })
      .where(eq(chat_rooms.id, room_id));
      
  } catch (error) {
    console.log(error);
  }
};

export const chatMessageByRoomId = async (room_id: number) => {
  return await db
    .select({
      message: await decryptField(chat_messages.message),
      file: chat_files,
      sender: {
        user_id: users.user_id,
        role: users.role,
        email: users.email,
        first_name: await decryptField(users.first_name),
        last_name: await decryptField(users.last_name)
      },
      created_at: chat_messages.createdAt
    })
    .from(chat_messages)
    .leftJoin(chat_files, eq(chat_messages.file_id, chat_files.file_id))
    .leftJoin(users, eq(chat_messages.sender_id, users.user_id))
    .where(eq(chat_messages.room_id, room_id))
    .orderBy(asc(chat_messages.createdAt));
};
