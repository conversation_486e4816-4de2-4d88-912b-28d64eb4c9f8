import { users } from '../db/schema/users';
import { db } from '../db';
import { chat_room_members, chat_rooms } from '../db/schema';
import { and, eq, sql } from 'drizzle-orm';
export const getChatRoomMembers = async (
  roomId: number,
) => {

  return await db
    .select({ user_id: chat_room_members.user_id })
    .from(chat_room_members)
    .where(eq(chat_room_members.room_id, roomId)
    );
};
export const addChatRoomMember = async (
  userId: number,
  roomId: number,
) => {

  const user = await db
    .select({ user_id: chat_room_members.user_id })
    .from(chat_room_members)
    .where(
      and(
        eq(chat_room_members.user_id, userId),
        eq(chat_room_members.room_id, roomId)
      )
    );
  if (user.length > 0) return

  await db
    .insert(chat_room_members)
    .values({
      user_id: userId,
      room_id: roomId,
    })
    .returning()
};

export const getRoomFromUser = async (
  userId: number
) => {
  return await db
    .select({ room_id: chat_room_members.room_id })
    .from(chat_room_members)
    .where(
      and(
        eq(chat_room_members.user_id, userId)
      )
    );

};

