import { users } from '../db/schema/users';
import { db } from '../db';
import { chat_rooms } from '../db/schema';
import { uuid } from 'drizzle-orm/pg-core/columns/uuid';
import { eq } from 'drizzle-orm';
export const createChatRoom = async (
  roomName: string,
  serviceKey: string,
  description: string
) => {
  return await db
    .insert(chat_rooms)
    .values({
      room_name: roomName,
      service_key: serviceKey,
      description: description,
      deleted: false
    })
    .returning({
      id: chat_rooms.id,
      room_identifier: chat_rooms.room_identifier
    });
};

export const getChatRoomByIdentifier = async (roomName: string) => {
  return await db
    .select()
    .from(chat_rooms)
    .where(eq(chat_rooms.room_identifier, roomName))
    .then((res) => {
      return res[0];
    });
};

export const getChatRoomById = async (id: number) => {
  return await db
    .select({ id: chat_rooms.id, room_identifier: chat_rooms.room_identifier })
    .from(chat_rooms)
    .where(eq(chat_rooms.id, id))
    .then((res) => {
      return res[0];
    });
};
