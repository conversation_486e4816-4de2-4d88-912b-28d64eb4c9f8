import { users } from '../db/schema/users';
import { db } from '../db';
import { chat_rooms, telehealth_services } from '../db/schema';
import { uuid } from 'drizzle-orm/pg-core/columns/uuid';
import { eq } from 'drizzle-orm';
export const createChatRoom = async (
  roomName: string,
  serviceKey: string,
  description: string
) => {
  return await db
    .insert(chat_rooms)
    .values({
      room_name: roomName,
      service_key: serviceKey,
      description: description,
      deleted: false
    })
    .returning({
      id: chat_rooms.id,
      room_identifier: chat_rooms.room_identifier
    });
};

export const getChatRoomByIdentifier = async (roomName: string) => {
  return await db
    .select()
    .from(chat_rooms)
    .where(eq(chat_rooms.room_identifier, roomName))
    .then((res) => {
      return res[0];
    });
};

export const getChatRoomWithServiceByIdentifier = async (roomName: string) => {
  return await db
    .select({
      // Room details
      id: chat_rooms.id,
      room_identifier: chat_rooms.room_identifier,
      room_name: chat_rooms.room_name,
      service_key: chat_rooms.service_key,
      description: chat_rooms.description,
      active: chat_rooms.active,
      deleted: chat_rooms.deleted,
      created_at: chat_rooms.created_at,
      updated_at: chat_rooms.updated_at,
      // Service details
      service: {
        id: telehealth_services.id,
        service_key: telehealth_services.service_key,
        service_name: telehealth_services.service_name,
        display_service_name: telehealth_services.display_service_name,
        service_type: telehealth_services.service_type,
        service_mode: telehealth_services.service_mode,
        amount: telehealth_services.amount
      }
    })
    .from(chat_rooms)
    .leftJoin(
      telehealth_services,
      eq(chat_rooms.service_key, telehealth_services.service_key)
    )
    .where(eq(chat_rooms.room_identifier, roomName))
    .then((res) => {
      return res[0];
    });
};

export const getChatRoomById = async (id: number) => {
  return await db
    .select({ id: chat_rooms.id, room_identifier: chat_rooms.room_identifier })
    .from(chat_rooms)
    .where(eq(chat_rooms.id, id))
    .then((res) => {
      return res[0];
    });
};
