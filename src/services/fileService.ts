import { db } from '../db';
import { chat_files } from '../db/schema/chat_files';

export const createFile = async (
  user_id: number,
  name: string,
  path: string,
  room_id: number
) => {
  return await db
    .insert(chat_files)
    .values({
      user_id: user_id,
      name: name,
      path: path,
      room_id: room_id
    })
    .returning({
      insertedId: chat_files.file_id
    });
};
