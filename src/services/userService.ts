import { and, eq, inArray } from 'drizzle-orm';
import { db } from '../db';
import {
  chat_room_members,
  users,
  telehealth_service_order,
  telehealth_services
} from '../db/schema';
import { decrypt<PERSON>ield } from '../db/custom/pgcrypto';

export const getUserByUserId = async (user_id: number) => {
  return await db
    .select({
      first_name: await decrypt<PERSON><PERSON>(users.first_name),
      last_name: await decrypt<PERSON><PERSON>(users.last_name),
      email: users.email,
      email2: users.email_2,
      phone: users.phone,
      user_id: users.user_id,
      status: users.status
    })
    .from(users)
    .where(eq(users.user_id, user_id))
    .then((res) => {
      return res[0];
    });
};

export const getUserByGuid = async (user_guid: string) => {
  return await db
    .select({
      first_name: await decrypt<PERSON><PERSON>(users.first_name),
      last_name: await decry<PERSON><PERSON><PERSON>(users.last_name),
      email: users.email,
      phone: users.phone,
      user_id: users.user_id,
      status: users.status
    })
    .from(users)
    .where(eq(users.user_guid, user_guid))
    .then((res) => {
      return res[0];
    });
};

export const updateOtp = async (user_id: number, otp: string) => {
  return await db
    .update(users)
    .set({ otp: JSON.stringify({ code: otp, created_at: Date.now() }) })
    .where(eq(users.user_id, user_id));
};

export const verifyOtp = async (user_id: string, otp: string) => {
  return await db
    .select({
      first_name: users.first_name,
      last_name: users.last_name,
      email: users.email,
      user_id: users.user_id,
      status: users.status,
      otp: users.otp
    })
    .from(users)
    .where(eq(users.user_guid, user_id))
    .then((res) => {
      if (res) {
        const dbotp = JSON.parse(res[0].otp as string);
        if (dbotp.code === otp) {
          return res[0];
        }
      }

      return null;
    });
};

export const getUserByRoomId = async (
  room_id: number,
  role: 'USER' | 'BUSER'
) => {
  return await db
    .select({
      first_name: await decryptField(users.first_name),
      last_name: await decryptField(users.last_name),
      email: users.email,
      email2: users?.email_2,
      phone: users.phone,
      user_id: users.user_id,
      status: users.status,
      user_guid: users.user_guid
    })
    .from(users)
    .leftJoin(chat_room_members, eq(users.user_id, chat_room_members.user_id))
    .where(and(eq(chat_room_members.room_id, room_id), eq(users.role, role)))
    .then((res) => {
      return res[0];
    });
};

export const getUserByEmailAndRole = async (
  email: string,
  role:
    | 'USER'
    | 'BUSER'
    | 'AUSER'
    | 'medical_assistant'
    | 'viewer'
    | 'support_user'
    | 'pharmacist'
    | 'PHARMACY'
    | 'GROUP_ADMIN'
    | 'SUPPORT_ADMIN'
) => {
  return await db
    .select({
      first_name: await decryptField(users.first_name),
      last_name: await decryptField(users.last_name),
      email: users.email,
      email2: users?.email_2,
      phone: users.phone,
      user_id: users.user_id,
      status: users.status,
      user_guid: users.user_guid,
      role: users.role
    })
    .from(users)
    .where(and(eq(users.email, email), eq(users.role, role)))
    .then((res) => {
      return res[0];
    });
};

export const verifyOtpByEmail = async (email: string, otp: string) => {
  return await db
    .select({
      first_name: users.first_name,
      last_name: users.last_name,
      email: users.email,
      user_id: users.user_id,
      status: users.status,
      user_guid: users.user_guid,
      otp: users.otp
    })
    .from(users)
    .where(eq(users.email, email))
    .then((res) => {
      if (res && res.length > 0) {
        const user = res[0];
        const dbotp = JSON.parse(user.otp as string);
        if (dbotp.code === otp) {
          return user;
        }
      }
      return null;
    });
};

export const getOrderByGuid = async (orderGuid: string) => {
  return await db
    .select({
      id: telehealth_service_order.id,
      order_guid: telehealth_service_order.order_guid,
      service_id: telehealth_service_order.service_id,
      answer_given_by: telehealth_service_order.answer_given_by,
      provider_id: telehealth_service_order.provider_id,
      status: telehealth_service_order.status,
      service_type: telehealth_service_order.service_type,
      // Service details
      service_key: telehealth_services.service_key,
      service_name: telehealth_services.service_name,
      display_service_name: telehealth_services.display_service_name
    })
    .from(telehealth_service_order)
    .leftJoin(
      telehealth_services,
      eq(telehealth_service_order.service_id, telehealth_services.id)
    )
    .where(eq(telehealth_service_order.order_guid, orderGuid))
    .then((res) => {
      return res[0];
    });
};

export const getPatientOrders = async (patientId: number) => {
  // First, get all orders for the patient with provider and service details
  const allOrders = await db
    .select({
      id: telehealth_service_order.id,
      order_guid: telehealth_service_order.order_guid,
      service_id: telehealth_service_order.service_id,
      answer_given_by: telehealth_service_order.answer_given_by,
      provider_id: telehealth_service_order.provider_id,
      status: telehealth_service_order.status,
      service_type: telehealth_service_order.service_type,
      created_at: telehealth_service_order.created_at,
      updated_at: telehealth_service_order.updated_at,
      prescription_delivery: telehealth_service_order.prescription_delivery,
      ravkoo_prescription_option:
        telehealth_service_order.ravkoo_prescription_option,
      pharmacy_name: telehealth_service_order.pharmacy_name,
      pharmacy_phone: telehealth_service_order.pharmacy_phone,
      pharmacy_address: telehealth_service_order.pharmacy_address,
      session_type: telehealth_service_order.session_type,
      // Provider details
      provider_first_name: await decryptField(users.first_name),
      provider_last_name: await decryptField(users.last_name),
      provider_email: users.email,
      provider_phone: users.phone,
      provider_role: users.role,
      // Service details
      service_name: telehealth_services.service_name,
      service_description: telehealth_services.description,
      service_title: telehealth_services.title,
      service_subtitle: telehealth_services.subtitle,
      service_key: telehealth_services.service_key,
      service_details: telehealth_services.service_details,
      service_mode: telehealth_services.service_mode,
      service_amount: telehealth_services.amount,
      display_service_name: telehealth_services.display_service_name,
      is_video_call: telehealth_services.is_video_call,
      is_audio_call: telehealth_services.is_audio_call
    })
    .from(telehealth_service_order)
    .leftJoin(users, eq(telehealth_service_order.provider_id, users.user_id))
    .leftJoin(
      telehealth_services,
      eq(telehealth_service_order.service_id, telehealth_services.id)
    )
    .where(
      and(
        eq(telehealth_service_order.answer_given_by, patientId),
        inArray(telehealth_service_order.status, ['accept', 'completed'])
      )
    )
    .orderBy(telehealth_service_order.created_at);

  // Group by service_id and provider_id, keeping only the latest order for each combination
  const groupedOrders = new Map();

  allOrders.forEach((order) => {
    const key = `${order.service_id}_${order.provider_id}`;

    if (!groupedOrders.has(key)) {
      groupedOrders.set(key, order);
    } else {
      // Keep the order with the latest created_at date
      const existingOrder = groupedOrders.get(key);
      if (existingOrder && order.created_at && existingOrder.created_at) {
        if (new Date(order.created_at) > new Date(existingOrder.created_at)) {
          groupedOrders.set(key, order);
        }
      }
    }
  });

  // Convert Map values back to array and sort by created_at
  return Array.from(groupedOrders.values()).sort((a, b) => {
    const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
    const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
    return dateA - dateB;
  });
};
