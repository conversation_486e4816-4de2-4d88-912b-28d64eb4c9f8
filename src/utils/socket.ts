import { Server, WebSocket } from 'ws';
import Redis, { RedisOptions } from 'ioredis';
import {
  addChatRoomMember,
  getChatRoomMembers
} from '../services/chatRoomMembersService';
import {
  sendChatMessage,
  sendChatMessageRoom
} from '../services/chatMessageService';
import ApiError from '../helper/ApiError';

interface User {
  id: string;
  status: 'online' | 'offline';
  typing: boolean;
  roomId: string;
}

interface Group {
  id: string;
  users: Set<string>;
}

interface Team {
  id: string;
  users: Set<string>;
}

export default class SocketServer {
  private wss: Server;
  private redis: Redis;
  private redisSub: Redis;
  private sockets: Map<string, WebSocket> = new Map();

  constructor(port: number, redisOptions: RedisOptions) {
    this.wss = new Server({ port });
    this.redis = new Redis(redisOptions);
    this.redisSub = new Redis(redisOptions);
    this.wss.on('connection', this.onConnection.bind(this));
    this.redisSub.subscribe('broadcast', 'sendMessage');
    this.redisSub.on('message', this.onRedisMessage.bind(this));
    console.log(`WebSocket server started on port ${port}`);
  }

  private onConnection(socket: WebSocket) {
    socket.on('message', this.onMessage.bind(this, socket));
    socket.on('close', this.onClose.bind(this, socket));
  }

  private async onMessage(socket: WebSocket, message: string) {
    try {
      if (message.toString() === 'ping') return socket.send('pong');
      const parsedMessage = JSON.parse(message);
      const { type, payload } = parsedMessage;
      switch (type) {
        case 'register':
          await this.registerUser(socket, payload);
          break;
        case 'joinGroup':
          await this.joinGroup(payload.userId, payload.groupId);
          break;
        case 'leaveGroup':
          await this.leaveGroup(payload.userId, payload.groupId);
          break;
        // case 'joinTeam':
        //   await this.joinTeam(payload.userId, payload.teamId);
        //   break;
        // case 'leaveTeam':
        //   await this.leaveTeam(payload.userId, payload.teamId);
        //   break;
        case 'broadcast':
          await this.broadcast(payload.message);
          break;
        case 'typing':
          await this.setTyping(payload.userId, payload.isTyping);
          break;
        case 'status':
          await this.setStatus(payload.userId, payload.status);
          break;
        case 'sendMessage':
          await this.sendMessage(
            socket,
            payload.sender_id,
            payload.receiverId,
            payload.message,
            payload.type,
            payload.roomId,
            payload.file_id,
            payload.file_path
          );
          break;
        default:
          console.log(`Unknown message type: ${type}`);
      }
    } catch (error: any) {
      throw new ApiError(
        400,
        error?.message || 'Socket: initialization failed'
      );
    }
  }

  private async onClose(socket: WebSocket) {
    for (const [userId, userSocket] of this.sockets.entries()) {
      if (userSocket === socket) {
        this.sockets.delete(userId);
        const user = JSON.parse(
          (await this.redis.hget('users', userId)) || '{}'
        ) as User;
        user.status = 'offline';
        await this.redis.hset('users', userId, JSON.stringify(user));
        break;
      }
    }
  }

  private async registerUser(socket: WebSocket, payload: any) {
    const user: User = {
      id: payload.id,
      status: 'online',
      typing: false,
      roomId: payload.roomId
    };
    try {
      this.sockets.set(payload.id.toString(), socket);
      //console.log("sockets", this.sockets);
      await this.redis.hset('users', payload.id, JSON.stringify(user));
      // console.log('User registered:', this.sockets.get(payload.id));
      // await createChatRoom('pateint_chat_provider','weight_loss' , 'weight_loss_chat');
      await addChatRoomMember(payload.id, payload.roomId);
      console.log('registerUser', Array.from(this.sockets.keys()));
    } catch (error: any) {
      throw new ApiError(
        400,
        error?.message || 'Socket: User Registration Failed'
      );
    }
  }

  private async joinGroup(userId: string, groupId: string) {
    await this.redis.sadd(`group:${groupId}`, userId);
  }

  private async leaveGroup(userId: string, groupId: string) {
    await this.redis.srem(`group:${groupId}`, userId);
  }

  private async joinTeam(userId: string, teamId: string) {
    await this.redis.sadd(`team:${teamId}`, userId);
  }

  private async leaveTeam(userId: string, teamId: string) {
    await this.redis.srem(`team:${teamId}`, userId);
  }

  private async broadcast(message: string) {
    await this.redis.publish('broadcast', message);
  }

  private async sendMessage(
    wSocket: WebSocket,
    sender_id: string,
    receiverId: string,
    message: string,
    type: 'user' | 'room' | 'team',
    roomId: number,
    file_id: number,
    file_path: string
  ) {
    try {
      if (type === 'user') {
        const socket = this.sockets.get(receiverId);
        if (socket) {
          socket.send(JSON.stringify({ type: 'message', sender_id, message }));
          await sendChatMessage(message, parseInt(sender_id), file_id);
        }
      } else if (type === 'room') {
        //  const groupUsers = await this.redis.smembers(`group:${roomId}`);
        const roomUsers = await getChatRoomMembers(roomId);
        const msg = {
          message: {
            sender_id,
            message,
            type: 'room',
            roomId: roomId
          },
          user: {
            name: 'user'
          },
          file: {
            path: file_path
          }
        };

        const filteredUsers = roomUsers.filter(
          (user) => user.user_id !== parseInt(sender_id)
        );

        // this.sockets.delete(sender_id);

        for (const user of filteredUsers) {
          const socket = this.sockets.get(user.user_id.toString());
          if (!socket) {
            this.sockets.set(user.user_id.toString(), wSocket);
          }

          if (socket) {
            socket.send(JSON.stringify(msg));
            socket.terminate();
          }
        }
        await sendChatMessageRoom(
          message,
          parseInt(sender_id),
          roomId,
          file_id
        );
      } else if (type === 'team') {
        const teamUsers = await this.redis.smembers(`team:${receiverId}`);
        for (const userId of teamUsers) {
          const socket = this.sockets.get(userId);
          if (socket) {
            socket.send(
              JSON.stringify({ type: 'message', sender_id, message })
            );
          }
        }
      }
    } catch (error: any) {
      throw new ApiError(400, error?.message || 'Socket: Message Sent Error');
    }
  }

  private onRedisMessage(channel: string, payload: string) {
    if (channel === 'broadcast') {
      for (const socket of this.sockets.values()) {
        socket.send(JSON.stringify({ type: 'broadcast', payload }));
      }
    } else if (channel === 'sendMessage' && payload) {
      const { sender_id, receiverId, message, type } = JSON.parse(payload) as {
        sender_id: string;
        receiverId: string;
        message: string;
        type: 'user' | 'room' | 'team';
      };
      //this.sendMessage(sender_id, receiverId, message, type, 0, 0, "");
    }
  }

  private async setTyping(userId: string, isTyping: boolean) {
    const user = JSON.parse(
      (await this.redis.hget('users', userId)) || '{}'
    ) as User;
    user.typing = isTyping;
    await this.redis.hset('users', userId, JSON.stringify(user));
  }

  private async setStatus(userId: string, status: 'online' | 'offline') {
    const user = JSON.parse(
      (await this.redis.hget('users', userId)) || '{}'
    ) as User;
    user.status = status;
    await this.redis.hset('users', userId, JSON.stringify(user));
  }
}
