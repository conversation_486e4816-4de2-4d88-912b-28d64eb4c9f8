import { Socket } from 'socket.io';
import { Server } from 'socket.io';
import { Server as HttpServer } from 'http';
import { sendChatMessageRoom } from '../services/chatMessageService';
const onlineUser = new Map();

import { createAdapter } from '@socket.io/redis-adapter';
import { Redis } from 'ioredis';
import config from '../config';
import { db } from '../db';
import { chat_messages, chat_rooms } from '../db/schema';
import { eq } from 'drizzle-orm';

const registerSocketServer = (server: HttpServer) => {
  // console.log(config.RADIS_SERVER, config.RADIS_PORT);
  // const pubClient = new Redis({
  //   host: config.RADIS_SERVER,
  //   port: config.RADIS_PORT
  // });
  // const subClient = pubClient.duplicate();

  const io = new Server(server, {
    cors: {
      origin: '*',
      credentials: true,
      methods: ['GET', 'POST']
    }
    // adapter: createAdapter(pubClient, subClient)
  });

  io.on('connection', (socket: Socket) => {
    console.log('a user connected', socket.id);
    socket.on('join', (data) => {
      console.log(data);
      onlineUser.set(data.user_id, socket.id);
      console.log(onlineUser);
      socket.join(data.room_id);
    });

    socket.on(
      'send-message',
      async ({ message, sender, room_id, file_id, file_path }) => {
        const msg = {
          message,
          type: 'room',
          roomId: room_id,
          sender,
          file: {
            path: file_path
          }
        };
        socket.broadcast.to(room_id).emit('receive-message', msg);
        await db
          .update(chat_rooms)
          .set({ last_message: message, last_message_at: new Date() })
          .where(eq(chat_rooms.id, room_id));
        await sendChatMessageRoom(
          message,
          parseInt(sender.user_id),
          room_id,
          file_id
        );
      }
    );

    socket.on(
      'toggle-room-status',
      async ({ room_id, user_id, patient_id, room_status }) => {
        const msg = {
          room_id,
          user_id,
          patient_id,
          room_status
        };

        console.log('toggle-room-status', msg);

        socket.broadcast.to(room_id).emit('toggle-room-status', msg);
      }
    );

    socket.on('disconnect', () => {
      for (const [userId, socketId] of onlineUser.entries()) {
        if (socketId === socket.id) {
          onlineUser.delete(userId);
          socket.broadcast.emit('user-broadcast-status');
        }
      }
    });
  });
};

export default registerSocketServer;
